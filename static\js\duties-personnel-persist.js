// Persist selected personnel per row for the main duties table
// This hooks into personnel-select <select> elements to save and restore choices

(function(){
  const STORAGE_KEY = 'selectedPersonnelAssignments';

  function load() {
    try {
      const raw = localStorage.getItem(STORAGE_KEY);
      return raw ? JSON.parse(raw) : {};
    } catch (e) {
      return {};
    }
  }

  function save(map) {
    try { localStorage.setItem(STORAGE_KEY, JSON.stringify(map)); } catch (e) {}
  }

  function handleChange(e){
    const select = e.target;
    if (!select.classList.contains('personnel-select')) return;
    const row = select.closest('tr');
    if (!row) return;
    const rowIndex = Array.from(row.parentNode.children).indexOf(row);
    const map = load();
    map[rowIndex] = select.value || '';
    save(map);
  }

  function restore(){
    const map = load();
    const rows = document.querySelectorAll('#dutyTableBody tr');
    rows.forEach((tr, idx) => {
      const sel = tr.querySelector('.personnel-select');
      if (sel && map[idx]) sel.value = map[idx];
    });
  }

  document.addEventListener('change', handleChange);
  document.addEventListener('DOMContentLoaded', restore);
})();

