<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جلب أفراد الموقع - تشخيص المشكلة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .debug-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .log-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        
        .test-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .location-select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .personnel-select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="debug-container">
            <div class="text-center mb-4">
                <h1>🔍 تشخيص مشكلة جلب أفراد الموقع</h1>
                <p class="lead text-muted">اختبار وتتبع مشكلة عدم ظهور الأفراد عند اختيار الموقع M16A4</p>
            </div>
            
            <!-- اختبار API مباشر -->
            <div class="test-section">
                <h4>🌐 اختبار API مباشر</h4>
                <div class="row">
                    <div class="col-md-6">
                        <label>اختبار موقع بـ ID:</label>
                        <input type="number" id="locationIdInput" class="form-control" placeholder="أدخل ID الموقع" value="1">
                        <button class="btn btn-primary mt-2" onclick="testLocationAPI()">اختبار API</button>
                    </div>
                    <div class="col-md-6">
                        <label>اختبار موقع بالاسم:</label>
                        <input type="text" id="locationNameInput" class="form-control" placeholder="أدخل اسم الموقع" value="M16A4">
                        <button class="btn btn-info mt-2" onclick="searchLocationByName()">البحث عن الموقع</button>
                    </div>
                </div>
            </div>
            
            <!-- محاكاة اختيار الموقع -->
            <div class="test-section">
                <h4>🎯 محاكاة اختيار الموقع</h4>
                <div class="row">
                    <div class="col-md-4">
                        <label>اختر الموقع:</label>
                        <select id="testLocationSelect" class="location-select" onchange="simulateLocationSelection(this.value, 0)">
                            <option value="">اختر الموقع</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label>قائمة الأفراد 1:</label>
                        <select id="testPersonnelSelect1" class="personnel-select" disabled>
                            <option value="">اختر الفرد</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label>قائمة الأفراد 2:</label>
                        <select id="testPersonnelSelect2" class="personnel-select" disabled>
                            <option value="">اختر الفرد</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- سجل الأحداث -->
            <div class="test-section">
                <h4>📋 سجل الأحداث</h4>
                <button class="btn btn-secondary btn-sm" onclick="clearLog()">مسح السجل</button>
                <div id="debugLog" class="log-section">
                    جاهز للاختبار...
                </div>
            </div>
            
            <!-- معلومات النظام -->
            <div class="test-section">
                <h4>ℹ️ معلومات النظام</h4>
                <div id="systemInfo" class="alert alert-info">
                    <div>🌐 URL الحالي: <span id="currentUrl"></span></div>
                    <div>🕒 الوقت: <span id="currentTime"></span></div>
                    <div>🔧 وضع التشخيص: مفعل</div>
                </div>
            </div>
            
            <!-- روابط سريعة -->
            <div class="test-section">
                <h4>🔗 روابط سريعة</h4>
                <div class="row text-center">
                    <div class="col-md-3">
                        <a href="/duties/" class="btn btn-outline-primary w-100" target="_blank">
                            📝 كشف الواجبات
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/locations/" class="btn btn-outline-success w-100" target="_blank">
                            📍 إدارة المواقع
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/personnel/" class="btn btn-outline-info w-100" target="_blank">
                            👥 إدارة الأفراد
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-warning w-100" onclick="runFullDiagnostic()">
                            🔍 تشخيص شامل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let locationPersonnelMap = {};
        
        // دالة لإضافة رسالة إلى السجل
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logElement = document.getElementById('debugLog');
            const messageElement = document.createElement('div');
            messageElement.className = type;
            messageElement.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(messageElement);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // مسح السجل
        function clearLog() {
            document.getElementById('debugLog').innerHTML = 'تم مسح السجل...';
        }
        
        // اختبار API مباشر
        async function testLocationAPI() {
            const locationId = document.getElementById('locationIdInput').value;
            if (!locationId) {
                addToLog('❌ يرجى إدخال ID الموقع', 'error');
                return;
            }
            
            addToLog(`🌐 اختبار API للموقع ID: ${locationId}`, 'info');
            
            try {
                const url = `/locations/${locationId}/personnel/list`;
                addToLog(`📡 جلب البيانات من: ${url}`, 'info');
                
                const response = await fetch(url);
                addToLog(`📊 Response Status: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    addToLog(`✅ Response Data: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success && data.personnel) {
                        addToLog(`👥 عدد الأفراد: ${data.personnel.length}`, 'success');
                        data.personnel.forEach((person, index) => {
                            addToLog(`  ${index + 1}. ${person.name} (${person.rank}) - ID: ${person.id}`, 'info');
                        });
                    }
                } else {
                    const errorText = await response.text();
                    addToLog(`❌ Error Response: ${errorText}`, 'error');
                }
                
            } catch (error) {
                addToLog(`❌ خطأ في الطلب: ${error.message}`, 'error');
            }
        }
        
        // البحث عن موقع بالاسم
        async function searchLocationByName() {
            const locationName = document.getElementById('locationNameInput').value;
            if (!locationName) {
                addToLog('❌ يرجى إدخال اسم الموقع', 'error');
                return;
            }
            
            addToLog(`🔍 البحث عن الموقع: ${locationName}`, 'info');
            
            try {
                // جلب جميع المواقع
                const response = await fetch('/locations/list');
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.success && data.locations) {
                        const matchingLocations = data.locations.filter(loc => 
                            loc.name.includes(locationName)
                        );
                        
                        addToLog(`📊 تم العثور على ${matchingLocations.length} موقع مطابق`, 'success');
                        
                        matchingLocations.forEach(loc => {
                            addToLog(`  - ID: ${loc.id}, Name: ${loc.name}`, 'info');
                        });
                        
                        // إضافة المواقع إلى القائمة المنسدلة
                        const select = document.getElementById('testLocationSelect');
                        select.innerHTML = '<option value="">اختر الموقع</option>';
                        
                        matchingLocations.forEach(loc => {
                            const option = document.createElement('option');
                            option.value = loc.id;
                            option.textContent = loc.name;
                            select.appendChild(option);
                        });
                        
                    } else {
                        addToLog('❌ لم يتم العثور على مواقع', 'error');
                    }
                } else {
                    addToLog(`❌ خطأ في جلب المواقع: ${response.status}`, 'error');
                }
                
            } catch (error) {
                addToLog(`❌ خطأ في البحث: ${error.message}`, 'error');
            }
        }
        
        // محاكاة اختيار الموقع
        async function simulateLocationSelection(locationId, rowIndex) {
            addToLog(`🎯 محاكاة اختيار الموقع: ${locationId}, الصف: ${rowIndex}`, 'info');
            
            if (!locationId) {
                addToLog('⚠️ لا يوجد موقع محدد', 'warning');
                return;
            }
            
            // استخدام نفس الدالة من duties.js
            await loadPersonnelForLocation(locationId, rowIndex);
        }
        
        // نسخة من دالة loadPersonnelForLocation مع تشخيص إضافي
        async function loadPersonnelForLocation(locationId, rowIndex) {
            addToLog(`🔍 loadPersonnelForLocation called with locationId: ${locationId}, rowIndex: ${rowIndex}`, 'info');
            
            if (!locationId) {
                addToLog('⚠️ لا يوجد موقع محدد - تفريغ قوائم الأفراد', 'warning');
                return;
            }

            try {
                addToLog(`👥 تحميل أفراد الموقع ${locationId} للصف ${rowIndex}...`, 'info');

                // التحقق من وجود البيانات في الذاكرة المؤقتة
                if (locationPersonnelMap[locationId]) {
                    addToLog(`💾 استخدام البيانات المحفوظة للموقع ${locationId}`, 'info');
                    updatePersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
                    return;
                }

                const url = `/locations/${locationId}/personnel/list`;
                addToLog(`🌐 جلب البيانات من: ${url}`, 'info');
                
                const response = await fetch(url);
                addToLog(`📡 Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    addToLog(`📊 Response data: ${JSON.stringify(data, null, 2)}`, 'info');
                    
                    if (data.success) {
                        addToLog(`✅ تم جلب ${data.personnel.length} فرد للموقع ${locationId}`, 'success');
                        
                        // حفظ البيانات في الذاكرة المؤقتة
                        locationPersonnelMap[locationId] = data.personnel;

                        // تحديث قوائم الأفراد في هذا الصف
                        updatePersonnelSelectsInRow(rowIndex, data.personnel);
                    } else {
                        addToLog(`❌ خطأ في تحميل أفراد الموقع: ${data.message || data.error}`, 'error');
                    }
                } else {
                    addToLog(`❌ خطأ في الاستجابة: ${response.status} - ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    addToLog(`Response body: ${errorText}`, 'error');
                }
            } catch (error) {
                addToLog(`❌ خطأ في تحميل أفراد الموقع: ${error.message}`, 'error');
            }
        }
        
        // تحديث قوائم الأفراد
        function updatePersonnelSelectsInRow(rowIndex, personnel) {
            addToLog(`🔄 updatePersonnelSelectsInRow called with rowIndex: ${rowIndex}, personnel count: ${personnel.length}`, 'info');

            // تحديث قوائم الاختبار
            const select1 = document.getElementById('testPersonnelSelect1');
            const select2 = document.getElementById('testPersonnelSelect2');
            
            [select1, select2].forEach((select, index) => {
                select.disabled = false;
                select.innerHTML = '<option value="">اختر الفرد</option>';
                
                personnel.forEach(person => {
                    const option = document.createElement('option');
                    option.value = person.id;
                    option.textContent = `${person.name} (${person.rank})`;
                    select.appendChild(option);
                });
                
                addToLog(`✅ تم تحديث قائمة الأفراد ${index + 1} بـ ${personnel.length} فرد`, 'success');
            });
        }
        
        // تشخيص شامل
        async function runFullDiagnostic() {
            addToLog('🔍 بدء التشخيص الشامل...', 'info');
            
            // اختبار 1: جلب جميع المواقع
            addToLog('📍 اختبار 1: جلب جميع المواقع', 'info');
            await searchLocationByName('');
            
            // اختبار 2: اختبار موقع M16A4
            addToLog('🎯 اختبار 2: البحث عن M16A4', 'info');
            document.getElementById('locationNameInput').value = 'M16A4';
            await searchLocationByName();
            
            // اختبار 3: اختبار API لأول موقع
            addToLog('🌐 اختبار 3: اختبار API لأول موقع', 'info');
            document.getElementById('locationIdInput').value = '1';
            await testLocationAPI();
            
            addToLog('✅ انتهى التشخيص الشامل', 'success');
        }
        
        // تهيئة الصفحة
        window.addEventListener('load', function() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
            
            addToLog('🚀 تم تحميل صفحة التشخيص', 'success');
            addToLog('📋 جاهز لاختبار مشكلة جلب أفراد الموقع', 'info');
            
            // تشغيل تشخيص أولي
            setTimeout(runFullDiagnostic, 1000);
        });
    </script>
</body>
</html>
