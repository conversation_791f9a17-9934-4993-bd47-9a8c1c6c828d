<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات كشف الواجبات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/receipts.css">
    <style>
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-secondary);
        }
        
        .test-title {
            color: #007bff;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .test-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h1 class="text-center mb-4">اختبار إصلاحات كشف الواجبات</h1>
        
        <!-- اختبار 1: جلب المواقع -->
        <div class="test-section">
            <div class="test-title">اختبار 1: جلب المواقع من API</div>
            <button class="btn btn-primary" onclick="testLocationsAPI()">اختبار جلب المواقع</button>
            <div id="locations-result" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- اختبار 2: جلب أفراد موقع -->
        <div class="test-section">
            <div class="test-title">اختبار 2: جلب أفراد موقع محدد</div>
            <div class="mb-3">
                <label for="location-select" class="form-label">اختر موقع:</label>
                <select id="location-select" class="form-select">
                    <option value="">اختر موقع</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="testLocationPersonnel()">اختبار جلب الأفراد</button>
            <div id="personnel-result" class="test-result" style="display: none;"></div>

            <!-- عرض الأفراد في قائمة منسدلة -->
            <div class="mt-3" id="personnel-dropdown-container" style="display: none;">
                <label for="personnel-dropdown" class="form-label">الأفراد المرتبطين بالموقع:</label>
                <select id="personnel-dropdown" class="form-select">
                    <option value="">اختر فرد</option>
                </select>
            </div>
        </div>
        
        <!-- اختبار 3: حفظ واستعادة البيانات -->
        <div class="test-section">
            <div class="test-title">اختبار 3: حفظ واستعادة البيانات</div>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-success" onclick="testSaveData()">اختبار حفظ البيانات</button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-info" onclick="testLoadData()">اختبار تحميل البيانات</button>
                </div>
            </div>
            <div id="data-result" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- اختبار 4: localStorage -->
        <div class="test-section">
            <div class="test-title">اختبار 4: حفظ واستعادة من localStorage</div>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-warning" onclick="testLocalStorageSave()">حفظ في localStorage</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-secondary" onclick="testLocalStorageLoad()">تحميل من localStorage</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-danger" onclick="clearLocalStorage()">مسح localStorage</button>
                </div>
            </div>
            <div id="localStorage-result" class="test-result" style="display: none;"></div>
        </div>

        <!-- اختبار 5: وظائف تفريغ الجدول -->
        <div class="test-section">
            <div class="test-title">اختبار 5: وظائف تفريغ الجدول (مع الاحتفاظ بالمواقع)</div>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-outline-warning" onclick="testClearMainTable()">تفريغ الجدول الرئيسي</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-warning" onclick="testClearPatrolTable()">تفريغ جدول الدوريات</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-warning" onclick="testClearShiftsTable()">تفريغ جدول المناوبين</button>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">ملاحظة: هذه الاختبارات تحاكي وظائف التفريغ بدون تأكيد المستخدم</small>
            </div>
            <div id="clear-result" class="test-result" style="display: none;"></div>
        </div>
        
        <!-- عرض النتائج -->
        <div class="test-section">
            <div class="test-title">سجل الاختبارات</div>
            <div id="test-log" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                <!-- سيتم إضافة النتائج هنا -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات للاختبار
        let testLog = [];
        
        // دالة لإضافة رسالة إلى السجل
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            testLog.push(`[${timestamp}] ${message}`);
            
            const logElement = document.getElementById('test-log');
            const messageElement = document.createElement('div');
            messageElement.className = `test-${type}`;
            messageElement.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(messageElement);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // اختبار 1: جلب المواقع
        async function testLocationsAPI() {
            addToLog('بدء اختبار جلب المواقع...', 'info');
            
            try {
                const response = await fetch('/api/locations');
                const data = await response.json();
                
                const resultDiv = document.getElementById('locations-result');
                resultDiv.style.display = 'block';
                
                if (data.success && data.locations) {
                    resultDiv.className = 'test-result test-success';
                    resultDiv.innerHTML = `
                        <strong>✅ نجح الاختبار!</strong><br>
                        تم جلب ${data.locations.length} موقع<br>
                        <small>المواقع: ${data.locations.map(l => l.name).join(', ')}</small>
                    `;
                    
                    // ملء قائمة المواقع للاختبار التالي
                    const locationSelect = document.getElementById('location-select');
                    locationSelect.innerHTML = '<option value="">اختر موقع</option>';
                    data.locations.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.id;
                        option.textContent = location.name;
                        locationSelect.appendChild(option);
                    });
                    
                    addToLog(`تم جلب ${data.locations.length} موقع بنجاح`, 'success');
                } else {
                    resultDiv.className = 'test-result test-error';
                    resultDiv.innerHTML = `<strong>❌ فشل الاختبار!</strong><br>الرسالة: ${data.message || 'خطأ غير معروف'}`;
                    addToLog('فشل في جلب المواقع', 'error');
                }
            } catch (error) {
                const resultDiv = document.getElementById('locations-result');
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result test-error';
                resultDiv.innerHTML = `<strong>❌ خطأ في الشبكة!</strong><br>${error.message}`;
                addToLog(`خطأ في جلب المواقع: ${error.message}`, 'error');
            }
        }
        
        // اختبار 2: جلب أفراد موقع
        async function testLocationPersonnel() {
            const locationId = document.getElementById('location-select').value;
            
            if (!locationId) {
                alert('يرجى اختيار موقع أولاً');
                return;
            }
            
            addToLog(`بدء اختبار جلب أفراد الموقع ${locationId}...`, 'info');
            
            try {
                const response = await fetch(`/locations/${locationId}/personnel/list`);
                const data = await response.json();
                
                const resultDiv = document.getElementById('personnel-result');
                resultDiv.style.display = 'block';
                
                if (data.success) {
                    resultDiv.className = 'test-result test-success';
                    resultDiv.innerHTML = `
                        <strong>✅ نجح الاختبار!</strong><br>
                        تم جلب ${data.count || 0} فرد للموقع<br>
                        ${data.personnel && data.personnel.length > 0 ?
                            `<small>الأفراد: ${data.personnel.map(p => `${p.name} (${p.rank})`).join(', ')}</small>` :
                            '<small>لا يوجد أفراد مرتبطين بهذا الموقع</small>'
                        }
                    `;

                    // ملء قائمة الأفراد المنسدلة
                    const personnelDropdown = document.getElementById('personnel-dropdown');
                    const dropdownContainer = document.getElementById('personnel-dropdown-container');

                    if (data.personnel && data.personnel.length > 0) {
                        personnelDropdown.innerHTML = '<option value="">اختر فرد</option>';
                        data.personnel.forEach(person => {
                            const option = document.createElement('option');
                            option.value = person.id;
                            option.textContent = `${person.name} (${person.rank})`;
                            personnelDropdown.appendChild(option);
                        });
                        dropdownContainer.style.display = 'block';
                    } else {
                        dropdownContainer.style.display = 'none';
                    }

                    addToLog(`تم جلب ${data.count || 0} فرد للموقع ${locationId}`, 'success');
                } else {
                    resultDiv.className = 'test-result test-error';
                    resultDiv.innerHTML = `<strong>❌ فشل الاختبار!</strong><br>الرسالة: ${data.message || 'خطأ غير معروف'}`;
                    addToLog(`فشل في جلب أفراد الموقع: ${data.message}`, 'error');
                }
            } catch (error) {
                const resultDiv = document.getElementById('personnel-result');
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result test-error';
                resultDiv.innerHTML = `<strong>❌ خطأ في الشبكة!</strong><br>${error.message}`;
                addToLog(`خطأ في جلب أفراد الموقع: ${error.message}`, 'error');
            }
        }
        
        // اختبار 3: حفظ البيانات
        async function testSaveData() {
            addToLog('بدء اختبار حفظ البيانات...', 'info');
            
            const testData = {
                assignmentData: {
                    headers: ['الرقم', 'الموقع', 'الفترة الأولى', 'الفترة الثانية'],
                    rows: [['1', 'موقع تجريبي', 'فرد تجريبي 1', 'فرد تجريبي 2']]
                },
                patrolData: {
                    headers: ['الرقم', 'الموقع', 'الدورية'],
                    rows: [['1', 'موقع دورية', 'فرد دورية']]
                },
                shiftsData: {
                    headers: ['الرقم', 'الموقع', 'المناوب'],
                    rows: [['1', 'موقع مناوبة', 'فرد مناوبة']]
                },
                timestamp: new Date().toISOString()
            };
            
            try {
                const response = await fetch('/duties/api/save-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('data-result');
                resultDiv.style.display = 'block';
                
                if (data.success) {
                    resultDiv.className = 'test-result test-success';
                    resultDiv.innerHTML = `<strong>✅ نجح حفظ البيانات!</strong><br>الرسالة: ${data.message}`;
                    addToLog('تم حفظ البيانات بنجاح في الخادم', 'success');
                } else {
                    resultDiv.className = 'test-result test-error';
                    resultDiv.innerHTML = `<strong>❌ فشل حفظ البيانات!</strong><br>الخطأ: ${data.error}`;
                    addToLog(`فشل في حفظ البيانات: ${data.error}`, 'error');
                }
            } catch (error) {
                const resultDiv = document.getElementById('data-result');
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result test-error';
                resultDiv.innerHTML = `<strong>❌ خطأ في الشبكة!</strong><br>${error.message}`;
                addToLog(`خطأ في حفظ البيانات: ${error.message}`, 'error');
            }
        }
        
        // اختبار تحميل البيانات
        async function testLoadData() {
            addToLog('بدء اختبار تحميل البيانات...', 'info');
            
            try {
                const response = await fetch('/duties/api/load-data');
                const data = await response.json();
                
                const resultDiv = document.getElementById('data-result');
                resultDiv.style.display = 'block';
                
                if (data.success && data.data) {
                    resultDiv.className = 'test-result test-success';
                    resultDiv.innerHTML = `
                        <strong>✅ نجح تحميل البيانات!</strong><br>
                        الطابع الزمني: ${data.timestamp}<br>
                        <small>تم تحميل البيانات المحفوظة</small>
                    `;
                    addToLog('تم تحميل البيانات بنجاح من الخادم', 'success');
                } else {
                    resultDiv.className = 'test-result test-info';
                    resultDiv.innerHTML = `<strong>ℹ️ لا توجد بيانات محفوظة</strong><br>الرسالة: ${data.message}`;
                    addToLog('لا توجد بيانات محفوظة في الخادم', 'info');
                }
            } catch (error) {
                const resultDiv = document.getElementById('data-result');
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result test-error';
                resultDiv.innerHTML = `<strong>❌ خطأ في الشبكة!</strong><br>${error.message}`;
                addToLog(`خطأ في تحميل البيانات: ${error.message}`, 'error');
            }
        }
        
        // اختبار localStorage
        function testLocalStorageSave() {
            addToLog('بدء اختبار حفظ localStorage...', 'info');
            
            const testData = {
                headers: ['الرقم', 'الموقع', 'اختبار'],
                rows: [['1', 'موقع localStorage', 'بيانات تجريبية']]
            };
            
            try {
                localStorage.setItem('testData', JSON.stringify(testData));
                localStorage.setItem('selectedLocations', JSON.stringify({main: {0: '1'}, patrol: {}, shifts: {}}));
                
                const resultDiv = document.getElementById('localStorage-result');
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result test-success';
                resultDiv.innerHTML = '<strong>✅ تم حفظ البيانات في localStorage بنجاح!</strong>';
                addToLog('تم حفظ البيانات في localStorage', 'success');
            } catch (error) {
                const resultDiv = document.getElementById('localStorage-result');
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result test-error';
                resultDiv.innerHTML = `<strong>❌ فشل حفظ localStorage!</strong><br>${error.message}`;
                addToLog(`فشل في حفظ localStorage: ${error.message}`, 'error');
            }
        }
        
        function testLocalStorageLoad() {
            addToLog('بدء اختبار تحميل localStorage...', 'info');
            
            try {
                const testData = localStorage.getItem('testData');
                const selectedLocations = localStorage.getItem('selectedLocations');
                
                const resultDiv = document.getElementById('localStorage-result');
                resultDiv.style.display = 'block';
                
                if (testData && selectedLocations) {
                    const parsedData = JSON.parse(testData);
                    const parsedLocations = JSON.parse(selectedLocations);
                    
                    resultDiv.className = 'test-result test-success';
                    resultDiv.innerHTML = `
                        <strong>✅ تم تحميل البيانات من localStorage!</strong><br>
                        البيانات: ${parsedData.headers.join(', ')}<br>
                        المواقع المحفوظة: ${Object.keys(parsedLocations).length} جدول
                    `;
                    addToLog('تم تحميل البيانات من localStorage بنجاح', 'success');
                } else {
                    resultDiv.className = 'test-result test-info';
                    resultDiv.innerHTML = '<strong>ℹ️ لا توجد بيانات في localStorage</strong>';
                    addToLog('لا توجد بيانات في localStorage', 'info');
                }
            } catch (error) {
                const resultDiv = document.getElementById('localStorage-result');
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result test-error';
                resultDiv.innerHTML = `<strong>❌ فشل تحميل localStorage!</strong><br>${error.message}`;
                addToLog(`فشل في تحميل localStorage: ${error.message}`, 'error');
            }
        }
        
        function clearLocalStorage() {
            localStorage.removeItem('testData');
            localStorage.removeItem('selectedLocations');
            localStorage.removeItem('assignmentData');
            localStorage.removeItem('patrolData');
            localStorage.removeItem('shiftsData');

            const resultDiv = document.getElementById('localStorage-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result test-info';
            resultDiv.innerHTML = '<strong>🗑️ تم مسح جميع بيانات localStorage</strong>';
            addToLog('تم مسح جميع بيانات localStorage', 'info');
        }

        // اختبارات وظائف تفريغ الجدول
        function testClearMainTable() {
            addToLog('اختبار تفريغ الجدول الرئيسي...', 'info');

            // محاكاة وجود بيانات
            const testData = {
                headers: ['الرقم', 'الموقع', 'الفترة الأولى', 'الفترة الثانية'],
                rows: [['1', '1', 'فرد 1', 'فرد 2'], ['2', '2', 'فرد 3', 'فرد 4']]
            };

            // محاكاة وجود مواقع مختارة
            const testLocations = {main: {0: '1', 1: '2'}};
            localStorage.setItem('selectedLocations', JSON.stringify(testLocations));
            localStorage.setItem('assignmentData', JSON.stringify(testData));

            const resultDiv = document.getElementById('clear-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result test-success';
            resultDiv.innerHTML = `
                <strong>✅ محاكاة تفريغ الجدول الرئيسي</strong><br>
                تم حفظ البيانات التجريبية في localStorage<br>
                <small>في التطبيق الحقيقي: سيتم تفريغ البيانات مع الاحتفاظ بالمواقع المختارة</small>
            `;
            addToLog('تم محاكاة تفريغ الجدول الرئيسي بنجاح', 'success');
        }

        function testClearPatrolTable() {
            addToLog('اختبار تفريغ جدول الدوريات...', 'info');

            const resultDiv = document.getElementById('clear-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result test-success';
            resultDiv.innerHTML = `
                <strong>✅ محاكاة تفريغ جدول الدوريات</strong><br>
                <small>في التطبيق الحقيقي: سيتم تفريغ البيانات مع الاحتفاظ بالمواقع وإعادة تحميل الأفراد</small>
            `;
            addToLog('تم محاكاة تفريغ جدول الدوريات بنجاح', 'success');
        }

        function testClearShiftsTable() {
            addToLog('اختبار تفريغ جدول المناوبين...', 'info');

            const resultDiv = document.getElementById('clear-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result test-success';
            resultDiv.innerHTML = `
                <strong>✅ محاكاة تفريغ جدول المناوبين</strong><br>
                <small>في التطبيق الحقيقي: سيتم تفريغ البيانات مع الاحتفاظ بالمواقع وإعادة تحميل الأفراد</small>
            `;
            addToLog('تم محاكاة تفريغ جدول المناوبين بنجاح', 'success');
        }
        
        // تحميل المواقع عند تحميل الصفحة
        window.addEventListener('DOMContentLoaded', function() {
            addToLog('تم تحميل صفحة الاختبار', 'info');
            testLocationsAPI();
        });
    </script>
</body>
</html>
