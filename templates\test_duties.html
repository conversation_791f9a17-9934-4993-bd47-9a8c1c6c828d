<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار كشف الواجبات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- مكتبة XLSX للتصدير -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --bg-light: #ecf0f1;
            --border-color: #bdc3c7;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .container {
            max-width: 1400px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
        }

        .table td {
            padding: 8px;
            vertical-align: middle;
            border: 1px solid var(--border-color);
        }

        .editable-cell {
            background: transparent;
            border: none;
            outline: none;
            width: 100%;
            padding: 4px;
            text-align: center;
        }

        .editable-cell:focus {
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid var(--accent-color);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .save-status {
            font-size: 0.9rem;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .save-status.success {
            color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .save-status.error {
            color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .save-status.info {
            color: #17a2b8;
            background-color: rgba(23, 162, 184, 0.1);
            border: 1px solid rgba(23, 162, 184, 0.2);
        }

        .save-status.hidden {
            opacity: 0;
            visibility: hidden;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <h1 class="text-center text-white mb-4">
                    <i class="fas fa-clipboard-list"></i> اختبار كشف الواجبات
                </h1>
            </div>
            <div class="col-md-4 text-right">
                <button type="button" class="btn btn-info me-2" onclick="reloadAllData()">
                    <i class="fas fa-sync-alt"></i> إعادة تحميل
                </button>
                <button type="button" class="btn btn-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
            </div>
        </div>

        <!-- Main Assignment Table -->
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-3">كشف الواجبات</h4>
                <div class="row">
                    <div class="col-md-3">
                        <label>اليوم:</label>
                        <input type="text" id="dayName" class="form-control" readonly style="background-color: #f8f9fa;">
                    </div>
                    <div class="col-md-3">
                        <label>التاريخ الهجري:</label>
                        <input type="text" id="hijriDate" class="form-control" readonly style="background-color: #f8f9fa;">
                    </div>
                    <div class="col-md-3">
                        <label>التاريخ الميلادي:</label>
                        <input type="text" id="gregorianDate" class="form-control" readonly style="background-color: #f8f9fa;">
                    </div>
                    <div class="col-md-3">
                        <label>رقم الكشف:</label>
                        <input type="text" id="receiptNumber" class="form-control" readonly style="background-color: #f8f9fa;">
                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- Table Controls -->
                <div class="table-controls mb-3">
                    <div class="row">
                        <div class="col-md-8">
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn('main', 'عمود جديد')">
                                <i class="fas fa-plus"></i> إضافة عمود
                            </button>
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow('main')">
                                <i class="fas fa-plus"></i> إضافة صف
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm me-2" onclick="clearAllAssignmentData()">
                                <i class="fas fa-eraser"></i> تفريغ الكشف
                            </button>
                        </div>
                        <div class="col-md-4 text-end">
                            <span id="saveStatus" class="save-status me-3"></span>
                            <button type="button" class="btn btn-primary btn-sm" onclick="saveAssignmentData()">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Main Table -->
                <div class="table-responsive">
                    <table class="table table-bordered" id="dutyTable">
                        <thead>
                            <!-- Dynamic header will be generated -->
                        </thead>
                        <tbody>
                            <!-- Dynamic rows will be generated -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Patrol Duties Table -->
        <div class="card">
            <div class="card-header text-center">
                <h5 class="mb-0">كشف واجبات الدوريات</h5>
            </div>
            <div class="card-body">
                <!-- Patrol Table Controls -->
                <div class="table-controls mb-3">
                    <div class="row">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn('patrol', 'عمود جديد')">
                                <i class="fas fa-plus"></i> إضافة عمود
                            </button>
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow('patrol')">
                                <i class="fas fa-plus"></i> إضافة صف
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Patrol Table -->
                <div class="table-responsive">
                    <table class="table table-bordered" id="patrolTable">
                        <thead>
                            <!-- Dynamic header will be generated -->
                        </thead>
                        <tbody>
                            <!-- Dynamic rows will be generated -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Shifts Table -->
        <div class="card">
            <div class="card-header text-center">
                <h5 class="mb-0">كشف المناوبين</h5>
            </div>
            <div class="card-body">
                <!-- Shifts Table Controls -->
                <div class="table-controls mb-3">
                    <div class="row">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn('shifts', 'عمود جديد')">
                                <i class="fas fa-plus"></i> إضافة عمود
                            </button>
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow('shifts')">
                                <i class="fas fa-plus"></i> إضافة صف
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Shifts Table -->
                <div class="table-responsive">
                    <table class="table table-bordered" id="shiftsTable">
                        <thead>
                            <!-- Dynamic header will be generated -->
                        </thead>
                        <tbody>
                            <!-- Dynamic rows will be generated -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/duties-test.js"></script>
</body>
</html>
