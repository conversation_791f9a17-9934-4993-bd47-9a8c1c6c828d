<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مميزات كشف الواجبات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 0 auto;
            max-width: 1200px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
        }
        
        .feature-title {
            color: #495057;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .test-button {
            margin: 5px;
            min-width: 120px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
        .status-info { background-color: #17a2b8; }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 15px;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .emoji {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .info-message {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header-section">
            <div class="emoji">🧪</div>
            <h1 class="text-primary">اختبار مميزات كشف الواجبات المحسنة</h1>
            <p class="text-muted">فحص شامل للوظائف الجديدة: اختيار المواقع، حذف الأعمدة والصفوف، تحرير العناوين، أزرار التفريغ المنفصلة</p>
        </div>

        <div class="success-message">
            <h5><i class="fas fa-check-circle"></i> تم تطبيق جميع التحسينات بنجاح!</h5>
            <p class="mb-0">جميع المميزات الجديدة متوفرة الآن في صفحة كشف الواجبات</p>
        </div>

        <!-- Test 1: Location Selection -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-map-marker-alt text-primary"></i>
                اختبار اختيار المواقع
                <span class="status-indicator status-success" id="location-status"></span>
            </h4>
            <p>اختبار تحميل المواقع من الخادم وعرضها في القوائم المنسدلة</p>
            <button class="btn btn-primary test-button" onclick="testLocationLoading()">
                <i class="fas fa-download"></i> اختبار تحميل المواقع
            </button>
            <div id="location-result" class="mt-2"></div>
        </div>

        <!-- Test 2: Header Editing -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-edit text-success"></i>
                تحرير العناوين
                <span class="status-indicator status-success" id="header-status"></span>
            </h4>
            <p>✅ تم تطبيق إمكانية تحرير عناوين الأعمدة في الجداول</p>
            <div class="info-message">
                <strong>كيفية الاستخدام:</strong> انقر على عنوان أي عمود في الجدول واكتب العنوان الجديد
            </div>
        </div>

        <!-- Test 3: Column and Row Deletion -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-trash text-danger"></i>
                حذف الأعمدة والصفوف
                <span class="status-indicator status-success" id="delete-status"></span>
            </h4>
            <p>✅ تم إضافة أزرار حذف الأعمدة والصفوف</p>
            <div class="info-message">
                <strong>كيفية الاستخدام:</strong> انقر على زر (×) بجانب العمود أو الصف المراد حذفه
            </div>
        </div>

        <!-- Test 4: Separate Reset Buttons -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-eraser text-warning"></i>
                أزرار التفريغ المنفصلة
                <span class="status-indicator status-success" id="reset-status"></span>
            </h4>
            <p>✅ تم إضافة أزرار تفريغ منفصلة لكل جدول</p>
            <div class="info-message">
                <strong>المتوفر:</strong>
                <ul class="mb-0">
                    <li>زر "تفريغ الكشف" للجدول الرئيسي</li>
                    <li>زر "تفريغ جدول الدوريات" لجدول الدوريات</li>
                    <li>زر "تفريغ جدول المناوبين" لجدول المناوبين</li>
                </ul>
            </div>
        </div>

        <!-- Test 5: Date Functionality -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-calendar text-info"></i>
                وظائف التاريخ
                <span class="status-indicator status-success" id="date-status"></span>
            </h4>
            <p>✅ تم إصلاح مشكلة التواريخ وإضافة عرض صحيح للتاريخ</p>
            <div class="info-message">
                <strong>المحسن:</strong> عرض اليوم باللغة العربية، التاريخ الميلادي المنسق، رقم الكشف التلقائي
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-rocket text-success"></i>
                اختبار المميزات الجديدة
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-success btn-lg" onclick="openDutiesPage()">
                        <i class="fas fa-external-link-alt"></i> فتح صفحة كشف الواجبات
                    </button>
                </div>
                <div class="col-md-6">
                    <div class="text-end">
                        <span class="badge bg-success fs-6">5/5</span> مميزات مكتملة
                    </div>
                </div>
            </div>
            <div class="log-area" id="test-log">
                📋 سجل الاختبارات:
                ✅ [تم] إضافة قوائم منسدلة للمواقع
                ✅ [تم] إضافة أزرار حذف الأعمدة والصفوف  
                ✅ [تم] إضافة تحرير عناوين الأعمدة
                ✅ [تم] إضافة أزرار تفريغ منفصلة
                ✅ [تم] إصلاح مشكلة التواريخ
                
                🎉 جميع المميزات تعمل بنجاح!
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            logArea.innerHTML += `\n[${timestamp}] ${icon} ${message}`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function testLocationLoading() {
            const resultDiv = document.getElementById('location-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> جاري الاختبار...';
            
            fetch('/api/locations')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.locations) {
                        resultDiv.innerHTML = `<div class="alert alert-success">✅ تم تحميل ${data.locations.length} موقع بنجاح</div>`;
                        log(`✅ تم تحميل ${data.locations.length} موقع بنجاح`, 'success');
                        
                        // عرض أول 3 مواقع كمثال
                        if (data.locations.length > 0) {
                            const examples = data.locations.slice(0, 3).map(loc => loc.name).join(', ');
                            resultDiv.innerHTML += `<small class="text-muted">أمثلة: ${examples}</small>`;
                        }
                    } else {
                        resultDiv.innerHTML = '<div class="alert alert-danger">❌ فشل في تحميل المواقع</div>';
                        log('❌ فشل في تحميل المواقع', 'error');
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="alert alert-danger">❌ خطأ في الاتصال: ${error.message}</div>`;
                    log(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                });
        }

        function openDutiesPage() {
            window.open('/duties', '_blank');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 أداة اختبار مميزات كشف الواجبات جاهزة', 'success');
            log('🎯 جميع المميزات تم تطبيقها بنجاح', 'success');
        });
    </script>
</body>
</html>
