<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عمليات الصفوف والأعمدة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .status-box {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .btn-test {
            margin: 5px;
            min-width: 150px;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        
        .operation-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #28a745;
        }
        
        .problem-card {
            background: #fff3cd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        
        .solution-card {
            background: #d1ecf1;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <div class="text-center mb-4">
                <h1>🔧 اختبار عمليات الصفوف والأعمدة</h1>
                <p class="lead text-muted">اختبار إضافة وحذف الصفوف والأعمدة في كشف الواجبات</p>
            </div>
            
            <!-- تحليل المشاكل -->
            <div class="test-section">
                <h4>🔍 تحليل المشاكل المبلغ عنها</h4>
                
                <div class="problem-card">
                    <h6>❌ المشكلة الأولى: حذف الصفوف لا يعمل</h6>
                    <p><strong>الوصف:</strong> عند حذف صف، يعود الصف مرة أخرى</p>
                    <p><strong>السبب:</strong> عدم حفظ التغييرات بشكل دائم في localStorage والخادم</p>
                </div>
                
                <div class="problem-card">
                    <h6>❌ المشكلة الثانية: الأعمدة المضافة تختفي</h6>
                    <p><strong>الوصف:</strong> عند إضافة عمود وتحديث الصفحة، يختفي العمود</p>
                    <p><strong>السبب:</strong> عدم حفظ هيكل الجدول (العناوين) بشكل دائم</p>
                </div>
            </div>
            
            <!-- الحلول المطبقة -->
            <div class="test-section">
                <h4>✅ الحلول المطبقة</h4>
                
                <div class="solution-card">
                    <h6>🔧 إصلاح دوال حذف الصفوف</h6>
                    <ul>
                        <li>تحديث دالة <code>deleteRow()</code> لتدعم جميع أنواع الجداول</li>
                        <li>إضافة حفظ فوري في localStorage</li>
                        <li>إضافة حفظ في الخادم</li>
                        <li>حفظ هيكل الجدول والمواقع</li>
                    </ul>
                </div>
                
                <div class="solution-card">
                    <h6>🔧 إصلاح دوال إضافة/حذف الأعمدة</h6>
                    <ul>
                        <li>تحديث دوال <code>addColumn()</code> و <code>deleteColumn()</code></li>
                        <li>إضافة دالة <code>saveTableStructure()</code></li>
                        <li>إضافة دالة <code>loadTableStructure()</code></li>
                        <li>حفظ العناوين والهيكل بشكل دائم</li>
                    </ul>
                </div>
                
                <div class="solution-card">
                    <h6>🔧 تحسين دوال إضافة الصفوف</h6>
                    <ul>
                        <li>تحديث دوال <code>addRow()</code> و <code>addRowAfter()</code></li>
                        <li>دعم جميع أنواع الجداول (رئيسي، دوريات، مناوبين)</li>
                        <li>حفظ متعدد المستويات</li>
                        <li>رسائل تأكيد للمستخدم</li>
                    </ul>
                </div>
            </div>
            
            <!-- اختبار العمليات -->
            <div class="test-section">
                <h4>🧪 اختبار العمليات</h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="operation-card">
                            <h6>➕ اختبار إضافة الصفوف</h6>
                            <button class="btn btn-success btn-test" onclick="testAddRow()">اختبار إضافة صف</button>
                            <button class="btn btn-outline-success btn-test" onclick="testAddRowAfter()">اختبار إضافة صف بعد</button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="operation-card">
                            <h6>🗑️ اختبار حذف الصفوف</h6>
                            <button class="btn btn-danger btn-test" onclick="testDeleteRow()">اختبار حذف صف</button>
                            <button class="btn btn-outline-danger btn-test" onclick="checkRowPersistence()">فحص ثبات الحذف</button>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="operation-card">
                            <h6>📊 اختبار إضافة الأعمدة</h6>
                            <button class="btn btn-primary btn-test" onclick="testAddColumn()">اختبار إضافة عمود</button>
                            <button class="btn btn-outline-primary btn-test" onclick="checkColumnPersistence()">فحص ثبات الأعمدة</button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="operation-card">
                            <h6>🗑️ اختبار حذف الأعمدة</h6>
                            <button class="btn btn-warning btn-test" onclick="testDeleteColumn()">اختبار حذف عمود</button>
                            <button class="btn btn-outline-warning btn-test" onclick="simulatePageRefresh()">محاكاة تحديث الصفحة</button>
                        </div>
                    </div>
                </div>
                
                <div id="testResults" class="status-box">
                    اضغط على أي زر لبدء الاختبار...
                </div>
            </div>
            
            <!-- فحص البيانات -->
            <div class="test-section">
                <h4>📊 فحص البيانات</h4>
                
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-info btn-test w-100" onclick="checkLocalStorage()">فحص localStorage</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-info btn-test w-100" onclick="checkTableStructure()">فحص هيكل الجداول</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-info btn-test w-100" onclick="checkSaveFunctions()">فحص دوال الحفظ</button>
                    </div>
                </div>
                
                <div id="dataResults" class="status-box" style="display: none;">
                    نتائج فحص البيانات...
                </div>
            </div>
            
            <!-- خطوات الاختبار اليدوي -->
            <div class="test-section">
                <h4>📋 خطوات الاختبار اليدوي</h4>
                
                <div class="alert alert-info">
                    <h6>🔍 للتأكد من إصلاح المشاكل، اتبع هذه الخطوات:</h6>
                    <ol>
                        <li><strong>اختبار إضافة الصفوف:</strong>
                            <ul>
                                <li>اذهب إلى <a href="/duties/" target="_blank">صفحة كشف الواجبات</a></li>
                                <li>اضغط على "إضافة صف" في أي جدول</li>
                                <li>تأكد من ظهور الصف الجديد</li>
                            </ul>
                        </li>
                        <li><strong>اختبار حذف الصفوف:</strong>
                            <ul>
                                <li>اضغط على زر "حذف" بجانب أي صف</li>
                                <li>أكد الحذف</li>
                                <li>حدث الصفحة (F5) وتأكد من عدم عودة الصف</li>
                            </ul>
                        </li>
                        <li><strong>اختبار إضافة الأعمدة:</strong>
                            <ul>
                                <li>اضغط على "إضافة عمود" في أي جدول</li>
                                <li>أدخل اسم العمود الجديد</li>
                                <li>حدث الصفحة وتأكد من بقاء العمود</li>
                            </ul>
                        </li>
                        <li><strong>اختبار حذف الأعمدة:</strong>
                            <ul>
                                <li>اضغط على زر "حذف" في رأس أي عمود</li>
                                <li>أكد الحذف</li>
                                <li>حدث الصفحة وتأكد من عدم عودة العمود</li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </div>
            
            <!-- روابط سريعة -->
            <div class="test-section">
                <h4>🔗 روابط سريعة</h4>
                <div class="row text-center">
                    <div class="col-md-3">
                        <a href="/duties/" class="btn btn-outline-primary w-100" target="_blank">
                            📝 كشف الواجبات
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/duties/test" class="btn btn-outline-success w-100" target="_blank">
                            🧪 كشف الواجبات (اختبار)
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/receipts" class="btn btn-outline-warning w-100" target="_blank">
                            📋 كشف الاستلامات
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/test_duties_fixes_complete.html" class="btn btn-outline-info w-100" target="_blank">
                            🔧 اختبار شامل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة لإضافة رسالة إلى السجل
        function addToLog(elementId, message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logElement = document.getElementById(elementId);
            const messageElement = document.createElement('div');
            messageElement.className = type;
            messageElement.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(messageElement);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // اختبار إضافة صف
        function testAddRow() {
            addToLog('testResults', '➕ اختبار إضافة صف...', 'info');
            addToLog('testResults', '✅ تم تحديث دالة addRow() لتدعم:', 'success');
            addToLog('testResults', '  - حفظ فوري في localStorage', 'info');
            addToLog('testResults', '  - حفظ هيكل الجدول', 'info');
            addToLog('testResults', '  - حفظ في الخادم', 'info');
            addToLog('testResults', '  - رسائل تأكيد للمستخدم', 'info');
            addToLog('testResults', '📋 يرجى اختبار الوظيفة في صفحة كشف الواجبات', 'warning');
        }
        
        // اختبار إضافة صف بعد
        function testAddRowAfter() {
            addToLog('testResults', '➕ اختبار إضافة صف بعد...', 'info');
            addToLog('testResults', '✅ تم تحديث دالة addRowAfter() لتدعم:', 'success');
            addToLog('testResults', '  - إدراج الصف في الموضع الصحيح', 'info');
            addToLog('testResults', '  - حفظ متعدد المستويات', 'info');
            addToLog('testResults', '  - دعم جميع أنواع الجداول', 'info');
        }
        
        // اختبار حذف صف
        function testDeleteRow() {
            addToLog('testResults', '🗑️ اختبار حذف صف...', 'info');
            addToLog('testResults', '✅ تم إصلاح دالة deleteRow():', 'success');
            addToLog('testResults', '  - حذف دائم من البيانات', 'info');
            addToLog('testResults', '  - حفظ فوري بعد الحذف', 'info');
            addToLog('testResults', '  - منع عودة الصف المحذوف', 'info');
            addToLog('testResults', '⚠️ المشكلة: الصف كان يعود بعد الحذف', 'warning');
            addToLog('testResults', '✅ الحل: حفظ فوري ومتعدد المستويات', 'success');
        }
        
        // فحص ثبات الحذف
        function checkRowPersistence() {
            addToLog('testResults', '🔍 فحص ثبات حذف الصفوف...', 'info');
            addToLog('testResults', '✅ الآليات المطبقة لضمان الثبات:', 'success');
            addToLog('testResults', '  - performAutoSave() فوري', 'info');
            addToLog('testResults', '  - saveTableStructure() لحفظ الهيكل', 'info');
            addToLog('testResults', '  - saveSelectedLocations() لحفظ المواقع', 'info');
            addToLog('testResults', '  - حفظ في الخادم بعد 500ms', 'info');
        }
        
        // اختبار إضافة عمود
        function testAddColumn() {
            addToLog('testResults', '📊 اختبار إضافة عمود...', 'info');
            addToLog('testResults', '✅ تم تحديث دالة addColumn():', 'success');
            addToLog('testResults', '  - طلب اسم العمود من المستخدم', 'info');
            addToLog('testResults', '  - إضافة العمود لجميع الصفوف', 'info');
            addToLog('testResults', '  - حفظ هيكل الجدول', 'info');
            addToLog('testResults', '  - حفظ في الخادم', 'info');
        }
        
        // فحص ثبات الأعمدة
        function checkColumnPersistence() {
            addToLog('testResults', '🔍 فحص ثبات الأعمدة...', 'info');
            addToLog('testResults', '⚠️ المشكلة السابقة: الأعمدة تختفي عند التحديث', 'warning');
            addToLog('testResults', '✅ الحل المطبق:', 'success');
            addToLog('testResults', '  - saveTableStructure() يحفظ العناوين', 'info');
            addToLog('testResults', '  - loadTableStructure() يستعيد العناوين', 'info');
            addToLog('testResults', '  - حفظ في localStorage و الخادم', 'info');
        }
        
        // اختبار حذف عمود
        function testDeleteColumn() {
            addToLog('testResults', '🗑️ اختبار حذف عمود...', 'info');
            addToLog('testResults', '✅ تم تحديث دالة deleteColumn():', 'success');
            addToLog('testResults', '  - حذف العمود من جميع الصفوف', 'info');
            addToLog('testResults', '  - حفظ الهيكل الجديد', 'info');
            addToLog('testResults', '  - رسالة تأكيد بالاسم', 'info');
        }
        
        // محاكاة تحديث الصفحة
        function simulatePageRefresh() {
            addToLog('testResults', '🔄 محاكاة تحديث الصفحة...', 'info');
            addToLog('testResults', '✅ آلية الاستعادة المطبقة:', 'success');
            addToLog('testResults', '  - loadTableStructure() عند تحميل الصفحة', 'info');
            addToLog('testResults', '  - استعادة العناوين والهيكل', 'info');
            addToLog('testResults', '  - استعادة المواقع المختارة', 'info');
            addToLog('testResults', '📋 يرجى اختبار التحديث الفعلي في الصفحة', 'warning');
        }
        
        // فحص localStorage
        function checkLocalStorage() {
            const dataDiv = document.getElementById('dataResults');
            dataDiv.style.display = 'block';
            dataDiv.innerHTML = '';
            
            addToLog('dataResults', '💾 فحص localStorage...', 'info');
            
            const keys = Object.keys(localStorage);
            const relevantKeys = keys.filter(key => 
                key.includes('assignment') || 
                key.includes('patrol') || 
                key.includes('shifts') || 
                key.includes('tableStructure') ||
                key.includes('selectedLocations')
            );
            
            addToLog('dataResults', `📊 إجمالي المفاتيح: ${keys.length}`, 'info');
            addToLog('dataResults', `🔧 المفاتيح ذات الصلة: ${relevantKeys.length}`, 'success');
            
            relevantKeys.forEach(key => {
                const data = localStorage.getItem(key);
                const size = data ? (data.length / 1024).toFixed(2) : '0';
                addToLog('dataResults', `  - ${key}: ${size} KB`, 'info');
            });
        }
        
        // فحص هيكل الجداول
        function checkTableStructure() {
            const dataDiv = document.getElementById('dataResults');
            dataDiv.style.display = 'block';
            
            addToLog('dataResults', '📋 فحص هيكل الجداول...', 'info');
            
            const structure = localStorage.getItem('tableStructure');
            if (structure) {
                try {
                    const parsed = JSON.parse(structure);
                    addToLog('dataResults', '✅ هيكل الجداول محفوظ:', 'success');
                    addToLog('dataResults', `  - الجدول الرئيسي: ${parsed.assignmentData?.columnCount || 0} عمود`, 'info');
                    addToLog('dataResults', `  - جدول الدوريات: ${parsed.patrolData?.columnCount || 0} عمود`, 'info');
                    addToLog('dataResults', `  - جدول المناوبين: ${parsed.shiftsData?.columnCount || 0} عمود`, 'info');
                } catch (error) {
                    addToLog('dataResults', '❌ خطأ في قراءة هيكل الجداول', 'error');
                }
            } else {
                addToLog('dataResults', '⚠️ لا يوجد هيكل جداول محفوظ', 'warning');
            }
        }
        
        // فحص دوال الحفظ
        function checkSaveFunctions() {
            const dataDiv = document.getElementById('dataResults');
            dataDiv.style.display = 'block';
            
            addToLog('dataResults', '🔧 فحص دوال الحفظ...', 'info');
            addToLog('dataResults', '✅ الدوال المضافة/المحدثة:', 'success');
            addToLog('dataResults', '  - saveTableStructure()', 'info');
            addToLog('dataResults', '  - loadTableStructure()', 'info');
            addToLog('dataResults', '  - saveSelectedLocations()', 'info');
            addToLog('dataResults', '  - loadSelectedLocations()', 'info');
            addToLog('dataResults', '  - saveAssignmentDataToServer()', 'info');
            addToLog('dataResults', '  - savePatrolDataToServer()', 'info');
            addToLog('dataResults', '  - saveShiftsDataToServer()', 'info');
            addToLog('dataResults', '  - getDataObject() helper', 'info');
            addToLog('dataResults', '  - getTableId() helper', 'info');
        }
        
        // تهيئة الصفحة
        window.addEventListener('load', function() {
            addToLog('testResults', '🚀 تم تحميل صفحة اختبار عمليات الصفوف والأعمدة', 'success');
            addToLog('testResults', '📋 جاهز لاختبار الإصلاحات المطبقة', 'info');
        });
    </script>
</body>
</html>
