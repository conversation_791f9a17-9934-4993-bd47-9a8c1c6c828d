<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات كشف الواجبات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .test-title {
            color: #007bff;
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .status-box {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .btn-test {
            margin: 5px;
            min-width: 150px;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .icon {
            font-size: 24px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <div class="text-center mb-4">
                <h1>🔧 اختبار إصلاحات كشف الواجبات</h1>
                <p class="lead text-muted">اختبار شامل لجميع الإصلاحات المطبقة</p>
            </div>
            
            <!-- حالة النظام -->
            <div class="test-section">
                <div class="test-title">📊 حالة النظام</div>
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-info btn-test" onclick="checkSystemStatus()">فحص حالة النظام</button>
                        <button class="btn btn-secondary btn-test" onclick="checkLocalStorage()">فحص localStorage</button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-warning btn-test" onclick="clearAllData()">مسح جميع البيانات</button>
                        <button class="btn btn-success btn-test" onclick="resetToDefaults()">إعادة تعيين افتراضية</button>
                    </div>
                </div>
                <div id="systemStatus" class="status-box">
                    اضغط على "فحص حالة النظام" لعرض التفاصيل...
                </div>
            </div>
            
            <!-- اختبار المواقع والأفراد -->
            <div class="test-section">
                <div class="test-title">🏢 اختبار المواقع والأفراد</div>
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-primary btn-test" onclick="testLocationLoading()">اختبار تحميل المواقع</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-primary btn-test" onclick="testPersonnelLoading()">اختبار جلب الأفراد</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-primary btn-test" onclick="testLocationSaving()">اختبار حفظ المواقع</button>
                    </div>
                </div>
                <div class="mt-3">
                    <label for="testLocationSelect" class="form-label">اختبار موقع:</label>
                    <select id="testLocationSelect" class="form-select">
                        <option value="">اختر موقع للاختبار</option>
                    </select>
                </div>
                <div id="locationTestResult" class="status-box" style="display: none;"></div>
            </div>
            
            <!-- اختبار الجداول -->
            <div class="test-section">
                <div class="test-title">📋 اختبار الجداول</div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="feature-card">
                            <h6><span class="icon">📝</span>الجدول الرئيسي</h6>
                            <button class="btn btn-sm btn-outline-primary w-100" onclick="testMainTable()">اختبار</button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card">
                            <h6><span class="icon">🚔</span>جدول الدوريات</h6>
                            <button class="btn btn-sm btn-outline-success w-100" onclick="testPatrolTable()">اختبار</button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card">
                            <h6><span class="icon">👥</span>جدول المناوبين</h6>
                            <button class="btn btn-sm btn-outline-warning w-100" onclick="testShiftsTable()">اختبار</button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card">
                            <h6><span class="icon">💾</span>الحفظ والاستعادة</h6>
                            <button class="btn btn-sm btn-outline-info w-100" onclick="testSaveRestore()">اختبار</button>
                        </div>
                    </div>
                </div>
                <div id="tableTestResult" class="status-box" style="display: none;"></div>
            </div>
            
            <!-- اختبار الإصلاحات المحددة -->
            <div class="test-section">
                <div class="test-title">🔧 اختبار الإصلاحات المحددة</div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6>✅ الإصلاحات المطبقة:</h6>
                            <ul class="mb-0">
                                <li>إضافة عمود الموقع لجدول الدوريات</li>
                                <li>إصلاح جلب الأفراد من API الصحيح</li>
                                <li>حفظ واستعادة المواقع المختارة</li>
                                <li>حفظ واستعادة هيكل الجداول</li>
                                <li>فصل البيانات بين الصفحات</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <h6>🧪 اختبارات مطلوبة:</h6>
                            <ol class="mb-0">
                                <li>اختر موقع وتأكد من جلب الأفراد</li>
                                <li>أضف/احذف صفوف وأعمدة</li>
                                <li>حدث الصفحة وتأكد من الاستعادة</li>
                                <li>امسح البيانات وتأكد من الفصل</li>
                            </ol>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <button class="btn btn-lg btn-success" onclick="runCompleteTest()">
                        🚀 تشغيل اختبار شامل
                    </button>
                </div>
                <div id="completeTestResult" class="status-box" style="display: none;"></div>
            </div>
            
            <!-- روابط سريعة -->
            <div class="test-section">
                <div class="test-title">🔗 روابط سريعة</div>
                <div class="row text-center">
                    <div class="col-md-3">
                        <a href="/duties/" class="btn btn-outline-primary w-100">
                            📝 كشف الواجبات (الأصلي)
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/duties/test" class="btn btn-outline-success w-100">
                            🧪 كشف الواجبات (اختبار)
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/receipts" class="btn btn-outline-warning w-100">
                            📋 كشف الاستلامات
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/fix_data_conflict.html" class="btn btn-outline-danger w-100">
                            🔧 إصلاح التضارب
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة لإضافة رسالة إلى السجل
        function addToLog(elementId, message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logElement = document.getElementById(elementId);
            const messageElement = document.createElement('div');
            messageElement.className = type;
            messageElement.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(messageElement);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // فحص حالة النظام
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = '';
            
            addToLog('systemStatus', '🔍 فحص حالة النظام...', 'info');
            
            // فحص localStorage
            const allKeys = Object.keys(localStorage);
            const dutiesKeys = allKeys.filter(key => key.startsWith('duties_'));
            const receiptsKeys = allKeys.filter(key => key.startsWith('receipts_'));
            const conflictingKeys = ['patrolData', 'shiftsData', 'assignmentData'];
            const conflicts = allKeys.filter(key => conflictingKeys.includes(key));
            
            addToLog('systemStatus', `📊 إجمالي المفاتيح: ${allKeys.length}`, 'info');
            addToLog('systemStatus', `🔧 مفاتيح كشف الواجبات: ${dutiesKeys.length}`, 'success');
            addToLog('systemStatus', `📋 مفاتيح كشف الاستلامات: ${receiptsKeys.length}`, 'success');
            
            if (conflicts.length > 0) {
                addToLog('systemStatus', `⚠️ مفاتيح متضاربة: ${conflicts.length}`, 'error');
                conflicts.forEach(key => addToLog('systemStatus', `  - ${key}`, 'error'));
            } else {
                addToLog('systemStatus', '✅ لا توجد مفاتيح متضاربة', 'success');
            }
            
            // فحص APIs
            testAPI();
        }
        
        // اختبار APIs
        async function testAPI() {
            try {
                addToLog('systemStatus', '🌐 اختبار APIs...', 'info');
                
                // اختبار API المواقع
                const locationsResponse = await fetch('/locations/api/list');
                if (locationsResponse.ok) {
                    const locationsData = await locationsResponse.json();
                    addToLog('systemStatus', `✅ API المواقع: ${locationsData.locations?.length || 0} موقع`, 'success');
                } else {
                    addToLog('systemStatus', '❌ فشل في API المواقع', 'error');
                }
                
            } catch (error) {
                addToLog('systemStatus', `❌ خطأ في اختبار APIs: ${error.message}`, 'error');
            }
        }
        
        // اختبار تحميل المواقع
        async function testLocationLoading() {
            const resultDiv = document.getElementById('locationTestResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '';
            
            addToLog('locationTestResult', '🏢 اختبار تحميل المواقع...', 'info');
            
            try {
                const response = await fetch('/locations/api/list');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.locations) {
                        addToLog('locationTestResult', `✅ تم تحميل ${data.locations.length} موقع`, 'success');
                        
                        // ملء قائمة الاختبار
                        const select = document.getElementById('testLocationSelect');
                        select.innerHTML = '<option value="">اختر موقع للاختبار</option>';
                        data.locations.forEach(location => {
                            const option = document.createElement('option');
                            option.value = location.id;
                            option.textContent = location.name;
                            select.appendChild(option);
                        });
                        
                        addToLog('locationTestResult', '✅ تم ملء قائمة المواقع', 'success');
                    } else {
                        addToLog('locationTestResult', '❌ فشل في تحميل المواقع', 'error');
                    }
                } else {
                    addToLog('locationTestResult', `❌ خطأ في الاستجابة: ${response.status}`, 'error');
                }
            } catch (error) {
                addToLog('locationTestResult', `❌ خطأ: ${error.message}`, 'error');
            }
        }
        
        // اختبار جلب الأفراد
        async function testPersonnelLoading() {
            const locationId = document.getElementById('testLocationSelect').value;
            if (!locationId) {
                alert('يرجى اختيار موقع أولاً');
                return;
            }
            
            const resultDiv = document.getElementById('locationTestResult');
            resultDiv.style.display = 'block';
            
            addToLog('locationTestResult', `👥 اختبار جلب أفراد الموقع ${locationId}...`, 'info');
            
            try {
                const response = await fetch(`/locations/${locationId}/personnel/list`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        addToLog('locationTestResult', `✅ تم جلب ${data.personnel?.length || 0} فرد`, 'success');
                        if (data.personnel && data.personnel.length > 0) {
                            data.personnel.slice(0, 3).forEach(person => {
                                addToLog('locationTestResult', `  - ${person.name} (${person.rank})`, 'info');
                            });
                        }
                    } else {
                        addToLog('locationTestResult', '❌ فشل في جلب الأفراد', 'error');
                    }
                } else {
                    addToLog('locationTestResult', `❌ خطأ في الاستجابة: ${response.status}`, 'error');
                }
            } catch (error) {
                addToLog('locationTestResult', `❌ خطأ: ${error.message}`, 'error');
            }
        }
        
        // اختبار شامل
        function runCompleteTest() {
            const resultDiv = document.getElementById('completeTestResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '';
            
            addToLog('completeTestResult', '🚀 بدء الاختبار الشامل...', 'info');
            
            // تشغيل جميع الاختبارات
            setTimeout(() => {
                checkSystemStatus();
                addToLog('completeTestResult', '✅ تم فحص حالة النظام', 'success');
            }, 500);
            
            setTimeout(() => {
                testLocationLoading();
                addToLog('completeTestResult', '✅ تم اختبار تحميل المواقع', 'success');
            }, 1000);
            
            setTimeout(() => {
                addToLog('completeTestResult', '🎉 تم الانتهاء من الاختبار الشامل', 'success');
                addToLog('completeTestResult', '📋 يرجى مراجعة النتائج أعلاه', 'info');
            }, 2000);
        }
        
        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل تريد مسح جميع البيانات؟')) {
                localStorage.clear();
                addToLog('systemStatus', '🗑️ تم مسح جميع البيانات', 'warning');
                setTimeout(() => location.reload(), 1000);
            }
        }
        
        // باقي الدوال...
        function checkLocalStorage() { checkSystemStatus(); }
        function testLocationSaving() { addToLog('locationTestResult', '💾 اختبار حفظ المواقع...', 'info'); }
        function testMainTable() { addToLog('tableTestResult', '📝 اختبار الجدول الرئيسي...', 'info'); document.getElementById('tableTestResult').style.display = 'block'; }
        function testPatrolTable() { addToLog('tableTestResult', '🚔 اختبار جدول الدوريات...', 'info'); document.getElementById('tableTestResult').style.display = 'block'; }
        function testShiftsTable() { addToLog('tableTestResult', '👥 اختبار جدول المناوبين...', 'info'); document.getElementById('tableTestResult').style.display = 'block'; }
        function testSaveRestore() { addToLog('tableTestResult', '💾 اختبار الحفظ والاستعادة...', 'info'); document.getElementById('tableTestResult').style.display = 'block'; }
        function resetToDefaults() { addToLog('systemStatus', '🔄 إعادة تعيين افتراضية...', 'info'); }
        
        // تهيئة الصفحة
        window.addEventListener('load', function() {
            setTimeout(checkSystemStatus, 500);
        });
    </script>
</body>
</html>
