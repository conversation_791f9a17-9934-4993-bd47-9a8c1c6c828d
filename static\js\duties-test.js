// Test version of duties.js - simplified for debugging

// Default headers constants
const DEFAULT_ASSIGNMENT_HEADERS = ['الرقم', 'موقع الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'];
const DEFAULT_PATROL_HEADERS = ['الرقم', 'موقع الدورية', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات'];
const DEFAULT_SHIFTS_HEADERS = ['الرقم', 'موقع المناوبة', 'المناوب الأول', 'المناوب الثاني', 'المناوب الثالث', 'ملاحظات'];

// Global data structures - مطابقة لكشف الاستلامات
let assignmentData = {
    headers: ['الرقم', 'موقع الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'],
    rows: [],
    rowIds: []
};

let patrolData = {
    headers: ['الرقم', 'موقع الدورية', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات'],
    rows: [],
    rowIds: []
};

let shiftsData = {
    headers: ['الرقم', 'موقع المناوبة', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات'],
    rows: [],
    rowIds: []
};

// Clean up conflicting localStorage data
function cleanupConflictingData() {
    console.log('🧹 تنظيف البيانات المتضاربة...');

    // إزالة المفاتيح القديمة التي تتضارب مع كشف الاستلامات
    const oldKeys = ['patrolData', 'shiftsData', 'selectedLocations', 'assignmentData'];
    oldKeys.forEach(key => {
        if (localStorage.getItem(key)) {
            console.log(`🗑️ إزالة المفتاح المتضارب: ${key}`);
            localStorage.removeItem(key);
        }
    });

    console.log('✅ تم تنظيف البيانات المتضاربة');
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة صفحة كشف الواجبات...');

    // تنظيف البيانات المتضاربة أولاً
    cleanupConflictingData();

    // Initialize data with default rows
    initializeDefaultData();
    
    // Generate tables
    generateTable('main');
    generateTable('patrol');
    generateTable('shifts');
    
    // Initialize form fields
    initializeFormFields();
    
    console.log('✅ تم تهيئة الصفحة بنجاح');
});

// Initialize default data
function initializeDefaultData() {
    console.log('📋 إضافة البيانات الافتراضية...');
    
    // Add default rows to main table
    for (let i = 0; i < 10; i++) {
        const newRow = Array(assignmentData.headers.length).fill('');
        newRow[0] = (i + 1).toString();
        assignmentData.rows.push(newRow);
        assignmentData.rowIds.push('row-' + Date.now() + '-' + i);
    }
    
    // Add default rows to patrol table
    for (let i = 0; i < 5; i++) {
        const newRow = Array(patrolData.headers.length).fill('');
        newRow[0] = (i + 1).toString();
        patrolData.rows.push(newRow);
        patrolData.rowIds.push('patrol-row-' + Date.now() + '-' + i);
    }
    
    // Add default rows to shifts table
    for (let i = 0; i < 5; i++) {
        const newRow = Array(shiftsData.headers.length).fill('');
        newRow[0] = (i + 1).toString();
        shiftsData.rows.push(newRow);
        shiftsData.rowIds.push('shifts-row-' + Date.now() + '-' + i);
    }
    
    console.log('✅ تم إضافة البيانات الافتراضية');
}

// Generate table with location dropdowns and editable headers
function generateTable(tableType) {
    console.log(`📊 إنشاء جدول: ${tableType}`);

    const tableId = getTableId(tableType);
    const table = document.getElementById(tableId);

    if (!table) {
        console.error(`❌ لم يتم العثور على الجدول: ${tableId}`);
        return;
    }

    const dataObj = getDataObject(tableType);

    // Clear existing content
    table.innerHTML = '';

    // Create header with editable headers and delete buttons
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    dataObj.headers.forEach((header, index) => {
        const th = document.createElement('th');
        // إزالة الأنماط المباشرة للاعتماد على CSS classes
        // th.style.textAlign = 'center';
        // th.style.padding = '8px';
        // th.style.backgroundColor = 'var(--bg-secondary)';
        // th.style.color = 'var(--text-primary)';
        // th.style.border = '1px solid var(--border-color)';
        th.style.position = 'relative';

        if (index === 0) {
            // First column (row number) - not editable
            th.textContent = header;
        } else {
            // Other columns - editable with delete button
            const headerContainer = document.createElement('div');
            headerContainer.style.display = 'flex';
            headerContainer.style.alignItems = 'center';
            headerContainer.style.justifyContent = 'center';
            headerContainer.style.gap = '5px';

            const headerInput = document.createElement('input');
            headerInput.type = 'text';
            headerInput.value = header;
            headerInput.style.background = 'transparent';
            headerInput.style.border = 'none';
            headerInput.style.color = 'var(--text-primary)';
            headerInput.style.textAlign = 'center';
            headerInput.style.fontSize = '14px';
            headerInput.style.fontWeight = 'bold';
            headerInput.style.width = '100%';

            headerInput.addEventListener('change', function() {
                updateHeader(tableType, index, this.value);
            });

            const deleteBtn = document.createElement('button');
            deleteBtn.innerHTML = '×';
            deleteBtn.className = 'btn btn-sm btn-danger';
            deleteBtn.style.padding = '2px 6px';
            deleteBtn.style.fontSize = '12px';
            deleteBtn.style.lineHeight = '1';
            deleteBtn.title = 'حذف العمود';

            deleteBtn.addEventListener('click', function() {
                deleteColumn(tableType, index);
            });

            headerContainer.appendChild(headerInput);
            if (index > 1) { // Don't allow deleting first two columns
                headerContainer.appendChild(deleteBtn);
            }

            th.appendChild(headerContainer);
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Create body with location dropdowns and delete buttons
    const tbody = document.createElement('tbody');

    dataObj.rows.forEach((row, rowIndex) => {
        const tr = document.createElement('tr');

        row.forEach((cell, cellIndex) => {
            const td = document.createElement('td');
            // إزالة الأنماط المباشرة للاعتماد على CSS classes
            // td.style.textAlign = 'center';
            // td.style.padding = '8px';
            // td.style.border = '1px solid var(--border-color)';
            // td.style.backgroundColor = 'var(--bg-primary)';
            // td.style.color = 'var(--text-primary)';

            if (cellIndex === 0) {
                // Row number with delete button
                const rowContainer = document.createElement('div');
                rowContainer.style.display = 'flex';
                rowContainer.style.alignItems = 'center';
                rowContainer.style.justifyContent = 'center';
                rowContainer.style.gap = '5px';

                const rowNumber = document.createElement('span');
                rowNumber.textContent = rowIndex + 1;

                const deleteRowBtn = document.createElement('button');
                deleteRowBtn.innerHTML = '×';
                deleteRowBtn.className = 'btn btn-sm btn-danger';
                deleteRowBtn.style.padding = '1px 4px';
                deleteRowBtn.style.fontSize = '10px';
                deleteRowBtn.style.lineHeight = '1';
                deleteRowBtn.title = 'حذف الصف';

                deleteRowBtn.addEventListener('click', function() {
                    deleteRow(tableType, rowIndex);
                });

                rowContainer.appendChild(rowNumber);
                rowContainer.appendChild(deleteRowBtn);
                td.appendChild(rowContainer);

            } else if (cellIndex === 1 && (tableType === 'main' || tableType === 'shifts' || tableType === 'patrol')) {
                // Location dropdown for main, shifts, and patrol tables
                const select = document.createElement('select');
                select.className = 'form-control location-select';
                select.style.background = 'var(--bg-primary)';
                select.style.color = 'var(--text-primary)';
                select.style.border = '1px solid var(--border-color)';

                // Add default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'اختر الموقع';
                select.appendChild(defaultOption);

                // Add location options (will be loaded from server)
                loadLocationOptions(select, cell);

                select.addEventListener('change', function() {
                    updateCellData(tableType, rowIndex, cellIndex, this.value);
                    // جلب الأفراد المرتبطين بالموقع المختار
                    if (this.value) {
                        loadLocationPersonnel(this.value, rowIndex, tableType);
                    } else {
                        clearPersonnelSelects(rowIndex, tableType);
                    }
                });

                td.appendChild(select);

            } else {
                // Regular editable cell
                const input = document.createElement('input');
                input.type = 'text';
                input.value = cell || '';
                input.className = 'form-control editable-cell';
                input.style.background = 'transparent';
                input.style.border = 'none';
                input.style.color = 'var(--text-primary)';
                input.style.textAlign = 'center';

                input.addEventListener('input', function() {
                    updateCellData(tableType, rowIndex, cellIndex, this.value);
                });

                td.appendChild(input);
            }

            tr.appendChild(td);
        });

        tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    console.log(`✅ تم إنشاء جدول ${tableType} بنجاح`);
}

// Get table ID by type
function getTableId(tableType) {
    switch (tableType) {
        case 'patrol': return 'patrolTable';
        case 'shifts': return 'shiftsTable';
        default: return 'dutyTable';
    }
}

// Get data object by type
function getDataObject(tableType) {
    switch (tableType) {
        case 'patrol': return patrolData;
        case 'shifts': return shiftsData;
        default: return assignmentData;
    }
}

// Update cell data
function updateCellData(tableType, rowIndex, columnIndex, value) {
    const dataObj = getDataObject(tableType);

    // Ensure row exists
    if (!dataObj.rows[rowIndex]) {
        dataObj.rows[rowIndex] = Array(dataObj.headers.length).fill('');
    }

    dataObj.rows[rowIndex][columnIndex] = value;
    console.log(`📝 تم تحديث الخلية: ${tableType}[${rowIndex}][${columnIndex}] = ${value}`);

    // حفظ تلقائي
    autoSave();
}

// Update header
function updateHeader(tableType, columnIndex, value) {
    const dataObj = getDataObject(tableType);
    dataObj.headers[columnIndex] = value;
    console.log(`📝 تم تحديث عنوان العمود ${columnIndex} في ${tableType} إلى: ${value}`);

    // حفظ تلقائي
    autoSave();
}

// Delete column
function deleteColumn(tableType, columnIndex) {
    if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
        const dataObj = getDataObject(tableType);

        // Remove from headers
        dataObj.headers.splice(columnIndex, 1);

        // Remove from all rows
        dataObj.rows.forEach(row => {
            row.splice(columnIndex, 1);
        });

        // Regenerate table
        generateTable(tableType);
        console.log(`🗑️ تم حذف العمود ${columnIndex} من ${tableType}`);
    }
}

// Delete row
function deleteRow(tableType, rowIndex) {
    if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
        const dataObj = getDataObject(tableType);

        // Remove row
        dataObj.rows.splice(rowIndex, 1);

        // Regenerate table
        generateTable(tableType);
        console.log(`🗑️ تم حذف الصف ${rowIndex} من ${tableType}`);
    }
}

// Load location options from server
function loadLocationOptions(selectElement, selectedValue = '') {
    fetch('/api/locations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == selectedValue) {
                        option.selected = true;
                    }
                    selectElement.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل المواقع:', error);
        });
}

// Load personnel for selected location
function loadLocationPersonnel(locationId, rowIndex, tableType) {
    console.log(`🔄 جلب أفراد الموقع ${locationId} للصف ${rowIndex} في جدول ${tableType}`);

    fetch(`/locations/${locationId}/personnel/list`)
        .then(response => {
            console.log(`📡 Response status: ${response.status}`);
            return response.json();
        })
        .then(data => {
            console.log(`📊 Response data:`, data);
            if (data.success && data.personnel) {
                console.log(`✅ تم جلب ${data.personnel.length} فرد للموقع ${locationId}`);
                console.log(`👥 الأفراد:`, data.personnel);
                updatePersonnelSelects(rowIndex, data.personnel, tableType);
            } else {
                console.log('⚠️ لا يوجد أفراد مرتبطين بهذا الموقع');
                console.log('🔍 سبب الفشل:', data.message || data.error || 'غير محدد');
                clearPersonnelSelects(rowIndex, tableType);
            }
        })
        .catch(error => {
            console.error('خطأ في جلب أفراد الموقع:', error);
            clearPersonnelSelects(rowIndex, tableType);
        });
}

// Update personnel select dropdowns in a row
function updatePersonnelSelects(rowIndex, personnel, tableType) {
    const tableId = getTableId(tableType);
    const table = document.getElementById(tableId);
    if (!table) return;

    const row = table.querySelector(`tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    console.log(`🔄 تحديث قوائم الأفراد للصف ${rowIndex} بـ ${personnel.length} فرد`);

    // البحث عن الخلايا التي تحتوي على حقول إدخال (ما عدا العمودين الأولين)
    const cells = row.querySelectorAll('td');

    // تحويل الخلايا من العمود الثالث فما فوق إلى قوائم منسدلة للأفراد
    for (let i = 2; i < cells.length; i++) {
        const cell = cells[i];
        const currentInput = cell.querySelector('input');

        if (currentInput) {
            // الحصول على القيمة الحالية إذا كانت موجودة
            const currentValue = currentInput.value;

            // إنشاء قائمة منسدلة للأفراد
            const select = document.createElement('select');
            select.className = 'form-control personnel-select';
            select.style.background = 'var(--bg-primary)';
            select.style.color = 'var(--text-primary)';
            select.style.border = '1px solid var(--border-color)';
            select.style.borderRadius = '4px';
            select.style.padding = '4px 8px';
            select.style.fontSize = '14px';
            select.style.width = '100%';

            // إضافة خيار افتراضي
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'اختر الفرد';
            select.appendChild(defaultOption);

            // إضافة الأفراد المرتبطين بالموقع
            personnel.forEach(person => {
                const option = document.createElement('option');
                option.value = person.name; // استخدام الاسم بدلاً من ID
                option.textContent = `${person.name} (${person.rank})`;
                option.setAttribute('data-person-id', person.id);
                option.setAttribute('data-person-rank', person.rank);

                // إذا كانت القيمة الحالية تطابق هذا الفرد، اختره
                if (currentValue && (currentValue === person.name || currentValue.includes(person.name))) {
                    option.selected = true;
                }

                select.appendChild(option);
            });

            // إضافة مستمع للتغيير
            select.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                let valueToSave = this.value;

                // إذا تم اختيار فرد، احفظ اسمه
                if (selectedOption && selectedOption.getAttribute('data-person-id')) {
                    valueToSave = selectedOption.textContent; // حفظ "الاسم (الرتبة)"
                }

                updateCellData(tableType, rowIndex, i, valueToSave);
                console.log(`✅ تم اختيار الفرد: ${valueToSave} في الخلية [${rowIndex}][${i}]`);
            });

            // استبدال حقل الإدخال بالقائمة المنسدلة
            cell.innerHTML = '';
            cell.appendChild(select);

            console.log(`✅ تم إنشاء قائمة أفراد للخلية [${rowIndex}][${i}] مع ${personnel.length} فرد`);
        }
    }
}

// Clear personnel selects in a row
function clearPersonnelSelects(rowIndex, tableType) {
    const tableId = getTableId(tableType);
    const table = document.getElementById(tableId);
    if (!table) return;

    const row = table.querySelector(`tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    console.log(`🧹 مسح قوائم الأفراد للصف ${rowIndex} وإعادة حقول الإدخال العادية`);

    // إعادة تحويل قوائم الأفراد إلى حقول إدخال عادية
    const cells = row.querySelectorAll('td');

    for (let i = 2; i < cells.length; i++) {
        const cell = cells[i];
        const currentSelect = cell.querySelector('select.personnel-select');

        if (currentSelect) {
            // الحصول على القيمة الحالية إذا كانت موجودة
            const currentValue = currentSelect.value || '';

            // إنشاء حقل إدخال عادي
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue;
            input.className = 'form-control editable-cell';
            input.style.background = 'transparent';
            input.style.border = 'none';
            input.style.color = 'var(--text-primary)';
            input.style.textAlign = 'center';
            input.style.padding = '4px';
            input.style.fontSize = '14px';
            input.style.width = '100%';

            input.addEventListener('input', function() {
                updateCellData(tableType, rowIndex, i, this.value);
            });

            // استبدال القائمة المنسدلة بحقل الإدخال
            cell.innerHTML = '';
            cell.appendChild(input);

            console.log(`✅ تم إعادة حقل الإدخال للخلية [${rowIndex}][${i}]`);
        }
    }
}

// Add column
function addColumn(tableType, headerText) {
    console.log(`➕ إضافة عمود جديد إلى ${tableType}: ${headerText}`);
    
    const dataObj = getDataObject(tableType);
    
    // Add header
    dataObj.headers.push(headerText);
    
    // Add empty cell to each row
    dataObj.rows.forEach(row => {
        row.push('');
    });
    
    // Regenerate table
    generateTable(tableType);
    
    console.log(`✅ تم إضافة العمود بنجاح`);
}

// Add row
function addRow(tableType) {
    console.log(`➕ إضافة صف جديد إلى ${tableType}`);
    
    const dataObj = getDataObject(tableType);
    
    // Create new row with empty cells
    const newRow = Array(dataObj.headers.length).fill('');
    newRow[0] = (dataObj.rows.length + 1).toString(); // Row number
    
    dataObj.rows.push(newRow);
    dataObj.rowIds.push(tableType + '-row-' + Date.now());
    
    // Regenerate table
    generateTable(tableType);
    
    console.log(`✅ تم إضافة الصف بنجاح`);
}

// Clear all data
function clearAllAssignmentData() {
    console.log('🗑️ تفريغ جميع البيانات...');
    
    // Clear all data
    assignmentData.rows = [];
    assignmentData.rowIds = [];
    patrolData.rows = [];
    patrolData.rowIds = [];
    shiftsData.rows = [];
    shiftsData.rowIds = [];
    
    // Reinitialize with default data
    initializeDefaultData();
    
    // Regenerate all tables
    generateTable('main');
    generateTable('patrol');
    generateTable('shifts');
    
    console.log('✅ تم تفريغ البيانات وإعادة التهيئة');
}

// Save data (placeholder)
function saveAssignmentData() {
    console.log('💾 حفظ البيانات...');
    
    const saveStatus = document.getElementById('saveStatus');
    if (saveStatus) {
        saveStatus.textContent = 'تم الحفظ';
        saveStatus.className = 'save-status success';
        
        setTimeout(() => {
            saveStatus.className = 'save-status hidden';
        }, 3000);
    }
    
    console.log('✅ تم حفظ البيانات (وهمي)');
}

// Initialize form fields
function initializeFormFields() {
    console.log('📝 تهيئة حقول النموذج...');
    
    const now = new Date();
    
    // Set day name
    const dayName = document.getElementById('dayName');
    if (dayName) {
        const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        dayName.value = days[now.getDay()];
    }
    
    // Set Gregorian date
    const gregorianDate = document.getElementById('gregorianDate');
    if (gregorianDate) {
        gregorianDate.value = now.toLocaleDateString('ar-SA');
    }
    
    // Set Hijri date (placeholder)
    const hijriDate = document.getElementById('hijriDate');
    if (hijriDate) {
        hijriDate.value = 'التاريخ الهجري';
    }
    
    // Set receipt number
    const receiptNumber = document.getElementById('receiptNumber');
    if (receiptNumber) {
        receiptNumber.value = '001';
    }
    
    console.log('✅ تم تهيئة حقول النموذج');
}

// Reload all data function
function reloadAllData() {
    console.log('🔄 إعادة تحميل جميع البيانات...');

    // Clear existing data
    assignmentData.rows = [];
    assignmentData.rowIds = [];
    patrolData.rows = [];
    patrolData.rowIds = [];
    shiftsData.rows = [];
    shiftsData.rowIds = [];

    // Reinitialize
    initializeDefaultData();
    generateTable('main');
    generateTable('patrol');
    generateTable('shifts');

    console.log('✅ تم إعادة تحميل البيانات بنجاح');
}

// Show alert function
function showAlert(message, type = 'info', duration = 3000) {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;

    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة التنبيه للصفحة
    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد المدة المحددة
    setTimeout(() => {
        if (alertDiv && alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}

// Get table data function
function getTableData(tableId) {
    console.log(`📊 استخراج بيانات الجدول: ${tableId}`);

    try {
        const table = document.getElementById(tableId);
        if (!table) {
            console.warn(`⚠️ الجدول ${tableId} غير موجود`);
            return { headers: [], rows: [] };
        }

        // استخراج العناوين
        const headers = [];
        const headerRow = table.querySelector('thead tr');
        if (headerRow) {
            const headerCells = headerRow.querySelectorAll('th');
            headerCells.forEach(cell => {
                headers.push(cell.textContent.trim());
            });
        }

        // استخراج البيانات
        const rows = [];
        const bodyRows = table.querySelectorAll('tbody tr');
        bodyRows.forEach(row => {
            const rowData = [];
            const cells = row.querySelectorAll('td');
            cells.forEach(cell => {
                // التحقق من وجود select أو input
                const select = cell.querySelector('select');
                const input = cell.querySelector('input');

                if (select) {
                    // إذا كان هناك select، أخذ النص المحدد
                    const selectedOption = select.options[select.selectedIndex];
                    rowData.push(selectedOption ? selectedOption.text : '');
                } else if (input) {
                    // إذا كان هناك input، أخذ القيمة
                    rowData.push(input.value || '');
                } else {
                    // أخذ النص العادي
                    rowData.push(cell.textContent.trim());
                }
            });

            // إضافة الصف فقط إذا كان يحتوي على بيانات
            if (rowData.some(cell => cell !== '')) {
                rows.push(rowData);
            }
        });

        console.log(`✅ تم استخراج ${headers.length} عمود و ${rows.length} صف من ${tableId}`);
        return { headers, rows };

    } catch (error) {
        console.error(`❌ خطأ في استخراج بيانات ${tableId}:`, error);
        return { headers: [], rows: [] };
    }
}

// Export to Excel using server-side processing (like receipts)
function exportToExcel() {
    console.log('📊 بدء تصدير Excel...');

    try {
        // Get data from tables
        const assignmentData = getTableData('dutyTable');
        const patrolData = getTableData('patrolTable');
        const shiftsData = getTableData('shiftsTable');

        console.log('📋 بيانات الجدول الرئيسي:', assignmentData);
        console.log('📋 بيانات جدول الدوريات:', patrolData);
        console.log('📋 بيانات جدول المناوبين:', shiftsData);

        // إعداد معلومات الكشف
        const now = new Date();
        const hijriDate = document.getElementById('hijriDate')?.value || now.toLocaleDateString('ar-SA-u-ca-islamic');
        const gregorianDate = document.getElementById('gregorianDate')?.value || now.toLocaleDateString('ar-SA');
        const dayName = document.getElementById('dayName')?.value || now.toLocaleDateString('ar-SA', { weekday: 'long' });
        const receiptNumber = document.getElementById('receiptNumber')?.value || `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-372`;

        // إعداد البيانات للتصدير
        const exportData = {
            hijriDate: hijriDate,
            gregorianDate: gregorianDate,
            dayName: dayName,
            receiptNumber: receiptNumber,
            dutyTableData: {
                headers: assignmentData.headers,
                rows: assignmentData.rows
            },
            patrolTableData: {
                headers: patrolData.headers,
                rows: patrolData.rows
            },
            shiftsTableData: {
                headers: shiftsData.headers,
                rows: shiftsData.rows
            }
        };

        console.log('📦 بيانات التصدير:', exportData);

        // إعداد headers للطلب
        const headers = {
            'Content-Type': 'application/json'
        };

        // إضافة CSRF token إذا كان متوفراً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken.getAttribute('content');
        }

        console.log('🌐 إرسال البيانات للخادم...');

        // إرسال البيانات للخادم لتصدير Excel
        fetch('/duties/export-excel', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(exportData)
        })
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            if (response.ok) {
                return response.blob();
            }
            throw new Error(`فشل في تصدير الملف: ${response.status} ${response.statusText}`);
        })
        .then(blob => {
            console.log('📁 تم استلام الملف، حجم:', blob.size, 'بايت');

            // تحميل الملف
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;

            // اسم الملف
            const fileName = `كشف_الواجبات_${gregorianDate || new Date().toISOString().split('T')[0]}`;
            const fileExtension = blob.type.includes('excel') || blob.type.includes('spreadsheet') ? '.xlsx' : '.csv';
            a.download = fileName + fileExtension;

            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('✅ تم تحميل الملف بنجاح');

            // رسالة نجاح
            const successMessage = '📊 تم تصدير كشف الواجبات بنجاح في ملف Excel';
            showAlert(successMessage, 'success', 6000);
        })
        .catch(error => {
            console.error('❌ خطأ في تصدير Excel:', error);

            // Fallback to client-side export
            console.log('🔄 التبديل إلى التصدير المحلي...');
            exportToExcelClientSide(assignmentData, patrolData, shiftsData);
        });

    } catch (error) {
        console.error('❌ خطأ في إعداد بيانات التصدير:', error);
        alert('حدث خطأ في إعداد البيانات للتصدير');
    }
}

// Client-side export fallback function
function exportToExcelClientSide(assignmentData, patrolData, shiftsData) {
    console.log('📊 تصدير محلي إلى Excel...');

    // Check if XLSX library is available
    if (typeof XLSX === 'undefined') {
        console.log('⚠️ مكتبة XLSX غير متوفرة، محاولة تحميلها...');

        // محاولة تحميل المكتبة ديناميكياً
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.onload = function() {
            console.log('✅ تم تحميل مكتبة XLSX بنجاح');
            setTimeout(() => exportToExcelClientSide(assignmentData, patrolData, shiftsData), 500);
        };
        script.onerror = function() {
            console.error('❌ فشل في تحميل مكتبة XLSX');
            alert('فشل في تحميل مكتبة التصدير. سيتم استخدام التصدير البديل.');
            exportToCSV(); // استخدام CSV كبديل
        };
        document.head.appendChild(script);
        return;
    }

    try {
        console.log('🎨 إنشاء ملف Excel بتصميم جميل...');

        // Create workbook
        const wb = XLSX.utils.book_new();

        // إعداد البيانات مع التصميم الجميل
        const now = new Date();
        const hijriDate = document.getElementById('hijriDate')?.value || now.toLocaleDateString('ar-SA-u-ca-islamic');
        const gregorianDate = document.getElementById('gregorianDate')?.value || now.toLocaleDateString('ar-SA');
        const dayName = document.getElementById('dayName')?.value || now.toLocaleDateString('ar-SA', { weekday: 'long' });
        const receiptNumber = document.getElementById('receiptNumber')?.value || `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-372`;

        // إنشاء البيانات للورقة الواحدة
        const worksheetData = [];

        // 1. رأس الصفحة - معلومات التاريخ (مطابق للصورة المطلوبة)
        worksheetData.push([gregorianDate, 'التاريخ الميلادي', hijriDate, 'التاريخ الهجري']);
        worksheetData.push([receiptNumber, 'رقم الكشف', dayName, 'اليوم']);
        worksheetData.push([]); // سطر فارغ

        // 2. عنوان كشف الواجبات الرئيسي
        const mainTitle = ['كشف الواجبات'];
        // ملء باقي الخلايا لدمج العنوان
        for (let i = 1; i < Math.max(assignmentData.headers.length, 8); i++) {
            mainTitle.push('');
        }
        worksheetData.push(mainTitle);

        // 3. رؤوس الجدول الرئيسي مع رقم الصف
        const mainHeaders = ['الرقم', 'موقع الإسناد', ...assignmentData.headers.slice(1)];
        worksheetData.push(mainHeaders);

        // 4. بيانات الجدول الرئيسي مع ترقيم
        assignmentData.rows.forEach((row, index) => {
            worksheetData.push([index + 1, ...row]);
        });

        worksheetData.push([]); // سطر فارغ

        // 5. عنوان كشف الدوريات
        const patrolTitle = ['كشف واجبات الدوريات'];
        for (let i = 1; i < Math.max(patrolData.headers.length, 8); i++) {
            patrolTitle.push('');
        }
        worksheetData.push(patrolTitle);

        // 6. رؤوس جدول الدوريات مع رقم الصف
        const patrolHeaders = ['الرقم', ...patrolData.headers];
        worksheetData.push(patrolHeaders);

        // 7. بيانات جدول الدوريات مع ترقيم
        patrolData.rows.forEach((row, index) => {
            worksheetData.push([index + 1, ...row]);
        });

        worksheetData.push([]); // سطر فارغ

        // 8. عنوان كشف المناوبين
        const shiftsTitle = ['كشف المناوبين'];
        for (let i = 1; i < Math.max(shiftsData.headers.length, 8); i++) {
            shiftsTitle.push('');
        }
        worksheetData.push(shiftsTitle);

        // 9. رؤوس جدول المناوبين مع رقم الصف
        const shiftsHeaders = ['الرقم', ...shiftsData.headers];
        worksheetData.push(shiftsHeaders);

        // 10. بيانات جدول المناوبين مع ترقيم
        shiftsData.rows.forEach((row, index) => {
            worksheetData.push([index + 1, ...row]);
        });

        // إنشاء الورقة
        const ws = XLSX.utils.aoa_to_sheet(worksheetData);

        // تطبيق التنسيقات (الألوان والحدود)
        applyExcelStyling(ws, worksheetData.length, Math.max(mainHeaders.length, 8));

        // دمج خلايا العناوين
        addMergedCells(ws, worksheetData, Math.max(mainHeaders.length, 8));

        // إضافة الورقة للكتاب
        XLSX.utils.book_append_sheet(wb, ws, 'كشف الواجبات الشامل');

        // Generate filename
        const filename = `كشف_الواجبات_${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        console.log('✅ تم تصدير الملف بتصميم جميل بنجاح');
        showAlert('📊 تم تصدير كشف الواجبات بنجاح', 'success', 4000);
    } catch (error) {
        console.error('❌ خطأ في التصدير:', error);
        alert('حدث خطأ أثناء التصدير');
    }
}

// دالة تطبيق التنسيقات على ملف Excel
function applyExcelStyling(worksheet, totalRows, totalCols) {
    console.log('🎨 تطبيق التنسيقات...');

    // إعداد عرض الأعمدة بشكل ذكي
    const colWidths = [];
    for (let i = 0; i < totalCols; i++) {
        if (i === 0) {
            colWidths.push({ wch: 8 }); // عمود الأرقام
        } else if (i === totalCols - 1) {
            colWidths.push({ wch: 25 }); // عمود الملاحظات أوسع
        } else {
            colWidths.push({ wch: 18 }); // باقي الأعمدة
        }
    }
    worksheet['!cols'] = colWidths;

    // إعداد ارتفاع الصفوف
    const rowHeights = [];
    for (let i = 0; i < totalRows; i++) {
        rowHeights.push({ hpt: 25 }); // ارتفاع موحد للصفوف
    }
    worksheet['!rows'] = rowHeights;

    // تطبيق التنسيقات على الخلايا
    const range = XLSX.utils.decode_range(worksheet['!ref']);

    for (let R = range.s.r; R <= range.e.r; ++R) {
        for (let C = range.s.c; C <= range.e.c; ++C) {
            const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });

            if (!worksheet[cellAddress]) {
                worksheet[cellAddress] = { t: 's', v: '' };
            }

            // تنسيق أساسي لجميع الخلايا
            worksheet[cellAddress].s = {
                font: {
                    name: 'Arial',
                    sz: 11,
                    bold: false
                },
                alignment: {
                    horizontal: 'center',
                    vertical: 'center',
                    wrapText: true
                },
                border: {
                    top: { style: 'thin', color: { rgb: '000000' } },
                    bottom: { style: 'thin', color: { rgb: '000000' } },
                    left: { style: 'thin', color: { rgb: '000000' } },
                    right: { style: 'thin', color: { rgb: '000000' } }
                }
            };

            // تنسيق خاص للعناوين والرؤوس
            const cellValue = worksheet[cellAddress].v;

            // التأكد من أن القيمة موجودة وتحويلها لنص إذا لزم الأمر
            const cellText = cellValue ? String(cellValue) : '';

            // رأس الصفحة (التاريخ والمعلومات)
            if (R <= 1) {
                worksheet[cellAddress].s.fill = {
                    fgColor: { rgb: 'F0F8E8' } // أخضر فاتح جداً
                };
                worksheet[cellAddress].s.font.bold = true;
                worksheet[cellAddress].s.border = {
                    top: { style: 'medium', color: { rgb: '2F5233' } },
                    bottom: { style: 'medium', color: { rgb: '2F5233' } },
                    left: { style: 'medium', color: { rgb: '2F5233' } },
                    right: { style: 'medium', color: { rgb: '2F5233' } }
                };
            }

            // عناوين الجداول الرئيسية
            if (cellText && (
                cellText.includes('كشف الواجبات') ||
                cellText.includes('كشف واجبات الدوريات') ||
                cellText.includes('كشف المناوبين')
            )) {
                worksheet[cellAddress].s.fill = {
                    fgColor: { rgb: 'A8D08D' } // أخضر متوسط
                };
                worksheet[cellAddress].s.font = {
                    name: 'Arial',
                    sz: 14,
                    bold: true,
                    color: { rgb: 'FFFFFF' } // نص أبيض
                };
                worksheet[cellAddress].s.border = {
                    top: { style: 'thick', color: { rgb: '2F5233' } },
                    bottom: { style: 'thick', color: { rgb: '2F5233' } },
                    left: { style: 'thick', color: { rgb: '2F5233' } },
                    right: { style: 'thick', color: { rgb: '2F5233' } }
                };
            }

            // رؤوس الأعمدة
            if (cellText && (
                cellText.includes('ملاحظات') ||
                cellText.includes('موقع') ||
                cellText.includes('من') ||
                cellText.includes('إلى') ||
                cellText.includes('الرقم') ||
                cellText.includes('ساعة') ||
                cellText.includes('طبيعة')
            )) {
                worksheet[cellAddress].s.fill = {
                    fgColor: { rgb: 'C5E0B4' } // أخضر فاتح للرؤوس
                };
                worksheet[cellAddress].s.font = {
                    name: 'Arial',
                    sz: 12,
                    bold: true,
                    color: { rgb: '2F5233' }
                };
                worksheet[cellAddress].s.border = {
                    top: { style: 'medium', color: { rgb: '2F5233' } },
                    bottom: { style: 'medium', color: { rgb: '2F5233' } },
                    left: { style: 'medium', color: { rgb: '2F5233' } },
                    right: { style: 'medium', color: { rgb: '2F5233' } }
                };
            }

            // عمود الأرقام
            if (C === 0 && R > 2) {
                worksheet[cellAddress].s.fill = {
                    fgColor: { rgb: 'E8F5E8' } // أخضر فاتح جداً
                };
                worksheet[cellAddress].s.font.bold = true;
            }

            // تلوين الصفوف المتناوبة للبيانات
            if (R > 4 && !cellText.includes('كشف') && cellText !== 'الرقم') {
                const isEvenRow = Math.floor(R / 2) % 2 === 0;
                if (isEvenRow && C > 0) {
                    worksheet[cellAddress].s.fill = {
                        fgColor: { rgb: 'F8FDF8' } // أخضر فاتح جداً للصفوف الزوجية
                    };
                }
            }
        }
    }

    console.log('✅ تم تطبيق التنسيقات بنجاح');
}

// دالة دمج خلايا العناوين
function addMergedCells(worksheet, worksheetData, totalCols) {
    console.log('🔗 دمج خلايا العناوين...');

    if (!worksheet['!merges']) {
        worksheet['!merges'] = [];
    }

    // البحث عن صفوف العناوين ودمجها
    worksheetData.forEach((row, rowIndex) => {
        const cellValue = row[0];
        const cellText = cellValue ? String(cellValue) : '';

        // دمج عناوين الجداول الرئيسية
        if (cellText && (
            cellText.includes('كشف الواجبات') ||
            cellText.includes('كشف واجبات الدوريات') ||
            cellText.includes('كشف المناوبين')
        )) {
            worksheet['!merges'].push({
                s: { r: rowIndex, c: 0 },
                e: { r: rowIndex, c: totalCols - 1 }
            });
        }
    });

    console.log('✅ تم دمج الخلايا بنجاح');
}

// Export to CSV as fallback
function exportToCSV() {
    console.log('📄 تصدير إلى CSV...');

    try {
        let csvContent = '';

        // إضافة معلومات الكشف
        csvContent += 'كشف الواجبات - الفريق الأمني\n';
        csvContent += '================================\n\n';

        // إضافة التاريخ
        const now = new Date();
        csvContent += `التاريخ: ${now.toLocaleDateString('ar-SA')}\n`;
        csvContent += `الوقت: ${now.toLocaleTimeString('ar-SA')}\n\n`;

        // إضافة الجدول الرئيسي
        csvContent += 'الجدول الرئيسي:\n';
        csvContent += assignmentData.headers.join(',') + '\n';
        assignmentData.rows.forEach(row => {
            csvContent += row.join(',') + '\n';
        });
        csvContent += '\n';

        // إضافة جدول الدوريات
        csvContent += 'واجبات الدوريات:\n';
        csvContent += patrolData.headers.join(',') + '\n';
        patrolData.rows.forEach(row => {
            csvContent += row.join(',') + '\n';
        });
        csvContent += '\n';

        // إضافة جدول المناوبين
        csvContent += 'كشف المناوبين:\n';
        csvContent += shiftsData.headers.join(',') + '\n';
        shiftsData.rows.forEach(row => {
            csvContent += row.join(',') + '\n';
        });

        // تحميل الملف
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `كشف_الواجبات_${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('✅ تم تصدير ملف CSV بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تصدير CSV:', error);
        alert('حدث خطأ أثناء التصدير');
    }
}

// Reset main table (assignments)
function resetHeaders() {
    if (confirm('هل أنت متأكد من تفريغ كشف التكليفات؟ سيتم الاحتفاظ بالمواقع وحذف البيانات الأخرى فقط.')) {
        console.log('🗑️ بدء تفريغ كشف التكليفات مع الاحتفاظ بالمواقع...');

        // Save current locations with their details
        const currentLocations = [];
        const locationSelects = document.querySelectorAll('#dutyTable .location-select');
        locationSelects.forEach((select, index) => {
            if (select.value) {
                currentLocations[index] = {
                    locationId: select.value,
                    locationName: select.options[select.selectedIndex].text
                };
                console.log(`💾 حفظ الموقع: ${currentLocations[index].locationName} في الصف ${index}`);
            }
        });

        // Reset data to defaults
        assignmentData.headers = [...DEFAULT_ASSIGNMENT_HEADERS];
        assignmentData.rows = [];

        // Create default rows
        for (let i = 0; i < 10; i++) {
            assignmentData.rows.push(Array(assignmentData.headers.length).fill(''));
        }

        // Restore locations in new rows
        currentLocations.forEach((locationData, index) => {
            if (assignmentData.rows[index] && locationData) {
                assignmentData.rows[index][1] = locationData.locationId;
            }
        });

        generateTable('main');

        // Restore locations in dropdowns and reload personnel
        setTimeout(() => {
            const newLocationSelects = document.querySelectorAll('#dutyTable .location-select');
            newLocationSelects.forEach((select, index) => {
                if (currentLocations[index]) {
                    select.value = currentLocations[index].locationId;

                    // إعادة تحميل أفراد الموقع
                    loadLocationPersonnel(currentLocations[index].locationId, index, 'main');

                    console.log(`✅ تم استعادة الموقع: ${currentLocations[index].locationName} في الصف ${index}`);
                }
            });

            // حفظ المواقع المستعادة
            saveSelectedLocations();
        }, 200);

        // حفظ تلقائي
        autoSave();

        console.log('✅ تم تفريغ كشف التكليفات مع الاحتفاظ بالمواقع وإعادة تحميل الأفراد');
    }
}

// Reset patrol table
function resetPatrolTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول واجبات الدوريات؟ سيتم الاحتفاظ بالمواقع وحذف البيانات الأخرى فقط.')) {
        console.log('🗑️ بدء تفريغ جدول الدوريات مع الاحتفاظ بالمواقع...');

        // Save current locations with their details
        const currentPatrolLocations = [];
        const patrolLocationSelects = document.querySelectorAll('#patrolTable .location-select');
        patrolLocationSelects.forEach((select, index) => {
            if (select.value) {
                currentPatrolLocations[index] = {
                    locationId: select.value,
                    locationName: select.options[select.selectedIndex].text
                };
                console.log(`💾 حفظ موقع الدورية: ${currentPatrolLocations[index].locationName} في الصف ${index}`);
            }
        });

        // Reset data to defaults
        patrolData.headers = [...DEFAULT_PATROL_HEADERS];
        patrolData.rows = [];

        // Create default rows
        for (let i = 0; i < 6; i++) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }

        // Restore locations in new rows
        currentPatrolLocations.forEach((locationData, index) => {
            if (patrolData.rows[index] && locationData) {
                patrolData.rows[index][1] = locationData.locationId;
            }
        });

        generateTable('patrol');

        // Restore locations in dropdowns and reload personnel
        setTimeout(() => {
            const newPatrolLocationSelects = document.querySelectorAll('#patrolTable .location-select');
            newPatrolLocationSelects.forEach((select, index) => {
                if (currentPatrolLocations[index]) {
                    select.value = currentPatrolLocations[index].locationId;

                    // إعادة تحميل أفراد الموقع
                    loadLocationPersonnel(currentPatrolLocations[index].locationId, index, 'patrol');

                    console.log(`✅ تم استعادة موقع الدورية: ${currentPatrolLocations[index].locationName} في الصف ${index}`);
                }
            });

            // حفظ المواقع المستعادة
            saveSelectedLocations();
        }, 200);

        // حفظ تلقائي
        autoSave();

        console.log('✅ تم تفريغ جدول واجبات الدوريات مع الاحتفاظ بالمواقع وإعادة تحميل الأفراد');
    }
}

// Reset shifts table
function resetShiftsTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول المناوبين؟ سيتم الاحتفاظ بالمواقع وإعادة تعيين العناوين للقيم الافتراضية.')) {
        // Save current locations
        const currentShiftsLocations = [];
        const shiftsLocationSelects = document.querySelectorAll('#shiftsTable .location-select');
        shiftsLocationSelects.forEach((select, index) => {
            if (select.value) {
                currentShiftsLocations[index] = select.value;
            }
        });

        // Reset data to defaults
        shiftsData.headers = [...DEFAULT_SHIFTS_HEADERS];
        shiftsData.rows = [];

        // Create one default row
        shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));

        // Restore locations in new rows
        currentShiftsLocations.forEach((locationId, rowIndex) => {
            if (locationId && shiftsData.rows[rowIndex]) {
                shiftsData.rows[rowIndex][1] = locationId;
            }
        });

        generateTable('shifts');

        // Restore locations in dropdowns and reload personnel
        setTimeout(() => {
            const newShiftsLocationSelects = document.querySelectorAll('#shiftsTable .location-select');
            newShiftsLocationSelects.forEach((select, index) => {
                if (currentShiftsLocations[index]) {
                    select.value = currentShiftsLocations[index];

                    // إعادة تحميل أفراد الموقع
                    loadLocationPersonnel(currentShiftsLocations[index], index, 'shifts');

                    console.log(`✅ تم استعادة موقع المناوبة في الصف ${index}`);
                }
            });

            // حفظ المواقع المستعادة
            saveSelectedLocations();
        }, 200);

        // حفظ تلقائي
        autoSave();

        console.log('✅ تم تفريغ جدول المناوبين مع الاحتفاظ بالمواقع وإعادة تحميل الأفراد');
    }
}

// Initialize dates
function initializeDates() {
    const now = new Date();

    // Set day name in Arabic
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = document.getElementById('dayName');
    if (dayName) {
        dayName.value = dayNames[now.getDay()];
    }

    // Set Gregorian date
    const gregorianDate = document.getElementById('gregorianDate');
    if (gregorianDate) {
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        gregorianDate.value = `${year}-${month}-${day}`;
    }

    // Set Hijri date (placeholder - you can integrate with a Hijri calendar library)
    const hijriDate = document.getElementById('hijriDate');
    if (hijriDate) {
        hijriDate.value = 'التاريخ الهجري';
    }

    // Set receipt number
    const receiptNumber = document.getElementById('receiptNumber');
    if (receiptNumber) {
        receiptNumber.value = '001';
    }

    console.log('✅ تم تهيئة التواريخ');
}

// Auto save function
function autoSave() {
    // حفظ البيانات
    localStorage.setItem('duties_assignmentData', JSON.stringify(assignmentData));
    localStorage.setItem('duties_patrolData', JSON.stringify(patrolData));
    localStorage.setItem('duties_shiftsData', JSON.stringify(shiftsData));

    // حفظ المواقع المختارة
    saveSelectedLocations();

    console.log('💾 تم الحفظ التلقائي في localStorage');
}

// Save selected locations
function saveSelectedLocations() {
    const locations = {
        main: {},
        patrol: {},
        shifts: {}
    };

    // حفظ مواقع الجدول الرئيسي
    const mainLocationSelects = document.querySelectorAll('#dutyTable .location-select');
    mainLocationSelects.forEach((select, index) => {
        if (select.value) {
            locations.main[index] = select.value;
        }
    });

    // حفظ مواقع جدول الدوريات
    const patrolLocationSelects = document.querySelectorAll('#patrolTable .location-select');
    patrolLocationSelects.forEach((select, index) => {
        if (select.value) {
            locations.patrol[index] = select.value;
        }
    });

    // حفظ مواقع جدول المناوبين
    const shiftsLocationSelects = document.querySelectorAll('#shiftsTable .location-select');
    shiftsLocationSelects.forEach((select, index) => {
        if (select.value) {
            locations.shifts[index] = select.value;
        }
    });

    localStorage.setItem('duties_selectedLocations', JSON.stringify(locations));
    console.log('📍 تم حفظ المواقع المختارة');
}

// Restore selected locations
function restoreSelectedLocations() {
    try {
        const savedLocations = localStorage.getItem('duties_selectedLocations');
        if (!savedLocations) return;

        const locations = JSON.parse(savedLocations);

        // استعادة مواقع الجدول الرئيسي
        setTimeout(() => {
            if (locations.main) {
                const mainLocationSelects = document.querySelectorAll('#dutyTable .location-select');
                mainLocationSelects.forEach((select, index) => {
                    if (locations.main[index]) {
                        select.value = locations.main[index];
                        // تحديث البيانات
                        const rowIndex = index;
                        const cellIndex = 1; // عمود الموقع
                        updateCellData('main', rowIndex, cellIndex, locations.main[index]);
                        // جلب الأفراد مع تأخير قصير
                        setTimeout(() => {
                            loadLocationPersonnel(locations.main[index], rowIndex, 'main');
                        }, 100 * (index + 1)); // تأخير متدرج لتجنب التحميل المتزامن

                        console.log(`🔄 استعادة موقع الجدول الرئيسي: ${locations.main[index]} في الصف ${index}`);
                    }
                });
            }

            // استعادة مواقع جدول الدوريات
            if (locations.patrol) {
                const patrolLocationSelects = document.querySelectorAll('#patrolTable .location-select');
                patrolLocationSelects.forEach((select, index) => {
                    if (locations.patrol[index]) {
                        select.value = locations.patrol[index];
                        const rowIndex = index;
                        const cellIndex = 1;
                        updateCellData('patrol', rowIndex, cellIndex, locations.patrol[index]);
                        // جلب الأفراد مع تأخير قصير
                        setTimeout(() => {
                            loadLocationPersonnel(locations.patrol[index], rowIndex, 'patrol');
                        }, 150 * (index + 1)); // تأخير متدرج

                        console.log(`🔄 استعادة موقع جدول الدوريات: ${locations.patrol[index]} في الصف ${index}`);
                    }
                });
            }

            // استعادة مواقع جدول المناوبين
            if (locations.shifts) {
                const shiftsLocationSelects = document.querySelectorAll('#shiftsTable .location-select');
                shiftsLocationSelects.forEach((select, index) => {
                    if (locations.shifts[index]) {
                        select.value = locations.shifts[index];
                        const rowIndex = index;
                        const cellIndex = 1;
                        updateCellData('shifts', rowIndex, cellIndex, locations.shifts[index]);
                        // جلب الأفراد مع تأخير قصير
                        setTimeout(() => {
                            loadLocationPersonnel(locations.shifts[index], rowIndex, 'shifts');
                        }, 200 * (index + 1)); // تأخير متدرج

                        console.log(`🔄 استعادة موقع جدول المناوبين: ${locations.shifts[index]} في الصف ${index}`);
                    }
                });
            }

            console.log('✅ تم استعادة المواقع المختارة');
        }, 500); // انتظار قصير للتأكد من تحميل الجداول

    } catch (error) {
        console.error('❌ خطأ في استعادة المواقع:', error);
    }
}

// Load saved data
function loadSavedData() {
    try {
        // تحميل البيانات المحفوظة
        const savedAssignmentData = localStorage.getItem('duties_assignmentData');
        const savedPatrolData = localStorage.getItem('duties_patrolData');
        const savedShiftsData = localStorage.getItem('duties_shiftsData');

        if (savedAssignmentData) {
            const parsed = JSON.parse(savedAssignmentData);
            assignmentData.headers = parsed.headers || DEFAULT_ASSIGNMENT_HEADERS;
            assignmentData.rows = parsed.rows || [];
            console.log('✅ تم تحميل بيانات الجدول الرئيسي المحفوظة');
        }

        if (savedPatrolData) {
            const parsed = JSON.parse(savedPatrolData);
            patrolData.headers = parsed.headers || DEFAULT_PATROL_HEADERS;
            patrolData.rows = parsed.rows || [];
            console.log('✅ تم تحميل بيانات جدول الدوريات المحفوظة');
        }

        if (savedShiftsData) {
            const parsed = JSON.parse(savedShiftsData);
            shiftsData.headers = parsed.headers || DEFAULT_SHIFTS_HEADERS;
            shiftsData.rows = parsed.rows || [];
            console.log('✅ تم تحميل بيانات جدول المناوبين المحفوظة');
        }

        return true;
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات المحفوظة:', error);
        return false;
    }
}

// Save to server
function saveToServer() {
    const dataToSave = {
        assignmentData,
        patrolData,
        shiftsData,
        timestamp: new Date().toISOString()
    };

    fetch('/duties/api/save-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSave)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ تم حفظ البيانات في الخادم');
        } else {
            console.error('❌ فشل في حفظ البيانات في الخادم:', data.error);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ البيانات في الخادم:', error);
    });
}

// Load from server
function loadFromServer() {
    fetch('/duties/api/load-data')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                assignmentData = data.data.assignmentData || assignmentData;
                patrolData = data.data.patrolData || patrolData;
                shiftsData = data.data.shiftsData || shiftsData;

                // إعادة إنشاء الجداول
                generateTable('main');
                generateTable('patrol');
                generateTable('shifts');

                console.log('✅ تم تحميل البيانات من الخادم');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل البيانات من الخادم:', error);
        });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة صفحة كشف الواجبات...');

    // Initialize dates first
    initializeDates();

    // Try to load saved data first
    const hasSavedData = loadSavedData();

    // If no saved data, initialize with defaults
    if (!hasSavedData) {
        initializeDefaultData();
    }

    // Generate all tables
    generateTable('main');
    generateTable('patrol');
    generateTable('shifts');

    // Restore selected locations
    restoreSelectedLocations();

    // Try to load from server as well
    loadFromServer();

    console.log('✅ تم تحميل الصفحة بنجاح');
});

console.log('📄 تم تحميل ملف duties-test.js');

// فحص مكتبة XLSX عند التحميل
setTimeout(() => {
    if (typeof XLSX !== 'undefined') {
        console.log('✅ مكتبة XLSX متوفرة ومحملة بنجاح');
    } else {
        console.log('⚠️ مكتبة XLSX غير محملة، سيتم تحميلها عند الحاجة');
    }
}, 1000);
