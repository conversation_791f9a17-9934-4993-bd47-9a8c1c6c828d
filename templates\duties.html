{% extends "base.html" %}

{% block title %}كشف الواجبات{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/receipts.css') }}">
<!-- مكتبة XLSX للتصدير -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h3 style="color: var(--text-primary) !important;">
                <i class="fas fa-clipboard-list"></i> كشف الواجبات
            </h3>
            <p class="text-muted">إنشاء وإدارة كشوف الواجبات والمناوبات</p>
        </div>
        <div class="col-md-4 text-right">
            <button type="button" class="btn btn-info me-2" onclick="reloadAllData()">
                <i class="fas fa-sync-alt"></i> إعادة تحميل
            </button>
            <button type="button" class="btn btn-success me-2" onclick="saveAssignmentData()">
                <i class="fas fa-save"></i> حفظ
            </button>
            <button type="button" class="btn btn-success" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> تصدير
            </button>
        </div>
    </div>

    <!-- Duty Form -->
    <div class="card">
        <div class="card-header text-center">
            <h4 class="mb-3" style="color: var(--text-primary) !important;">كشف الواجبات</h4>
            <div class="row">
                <div class="col-md-3">
                    <label>اليوم:</label>
                    <input type="text" id="dayName" class="form-control" readonly>
                </div>
                <div class="col-md-3">
                    <label>التاريخ الهجري:</label>
                    <input type="text" id="hijriDate" class="form-control" readonly>
                </div>
                <div class="col-md-3">
                    <label>التاريخ الميلادي:</label>
                    <input type="text" id="gregorianDate" class="form-control" readonly>
                </div>
                <div class="col-md-3">
                    <label>رقم الكشف:</label>
                    <input type="text" id="receiptNumber" class="form-control" readonly>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn('main', 'عمود جديد')">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow('main')">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-warning btn-sm me-2" onclick="resetHeaders()">
                            <i class="fas fa-eraser"></i> تفريغ الكشف
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main Duty Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="dutyTable">
                    <thead id="tableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="dutyTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Patrol Duties Table -->
    <div class="card mt-4">
        <div class="card-header text-center">
            <h5 class="mb-0" style="color: var(--text-primary) !important;">كشف واجبات الدوريات</h5>
        </div>
        <div class="card-body">
            <!-- Patrol Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn('patrol', 'عمود جديد')">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow('patrol')">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-warning btn-sm me-2" onclick="resetPatrolTable()">
                            <i class="fas fa-eraser"></i> تفريغ جدول الدوريات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Patrol Duties Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="patrolTable">
                    <thead id="patrolTableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="patrolTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Shifts Table -->
    <div class="card mt-4">
        <div class="card-header text-center">
            <h5 class="mb-0" style="color: var(--text-primary) !important;">كشف المناوبين</h5>
        </div>
        <div class="card-body">
            <!-- Shifts Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn('shifts', 'عمود جديد')">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow('shifts')">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-warning btn-sm me-2" onclick="resetShiftsTable()">
                            <i class="fas fa-eraser"></i> تفريغ جدول المناوبين
                        </button>
                    </div>
                </div>
            </div>

            <!-- Shifts Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="shiftsTable">
                    <thead id="shiftsTableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="shiftsTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/duties-test.js') }}"></script>

{% block extra_scripts %}
<script src="/static/js/duties-personnel-persist.js"></script>
{% endblock %}

{% endblock %}