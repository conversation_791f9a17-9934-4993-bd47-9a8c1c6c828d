from flask import Blueprint, render_template, request, jsonify, session, send_file, make_response
from flask_login import login_required, current_user
from datetime import datetime, date, time
import json
from io import BytesIO
import csv
import tempfile
import os
from db import db
from models import DutyData, DutyPersonnel, DutyTemplate, Location, Personnel, LocationPersonnel, PatrolData, ShiftsData
from datetime_utils import get_saudi_now

# محاولة استيراد مكتبات Excel
try:
    import pandas as pd
    import xlsxwriter
    EXCEL_AVAILABLE = True
    print("✅ مكتبات Excel متوفرة")
except ImportError:
    EXCEL_AVAILABLE = False
    print("⚠️ مكتبات Excel غير متوفرة، سيتم استخدام CSV كبديل")

# إنشاء Blueprint
duties_bp = Blueprint('duties', __name__, url_prefix='/duties')

@duties_bp.route('/')
@login_required
def index():
    """صفحة كشف الواجبات الرئيسية"""
    return render_template('duties.html')

@duties_bp.route('/api/get-locations', methods=['GET'])
@login_required
def get_locations():
    """الحصول على قائمة المواقع المتاحة"""
    try:
        locations = Location.query.filter_by(status='نشط').order_by(Location.name).all()
        
        locations_data = []
        for location in locations:
            locations_data.append({
                'id': location.id,
                'name': location.name,
                'serial_number': location.serial_number,
                'type': location.type,
                'description': location.description
            })
        
        return jsonify({
            'success': True,
            'locations': locations_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/locations', methods=['GET'])
@login_required
def api_locations():
    """API endpoint للمواقع (مسار بديل)"""
    try:
        locations = Location.query.filter_by(status='نشط').order_by(Location.name).all()

        locations_data = []
        for location in locations:
            locations_data.append({
                'id': location.id,
                'name': location.name,
                'serial_number': location.serial_number,
                'type': location.type,
                'description': location.description
            })

        return jsonify({
            'success': True,
            'locations': locations_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/save-data', methods=['POST'])
@login_required
def save_duties_data():
    """حفظ بيانات كشف الواجبات"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات للحفظ'})

        # حذف البيانات السابقة للمستخدم الحالي
        DutyData.query.filter_by(user_id=current_user.id).delete()

        # حفظ البيانات الجديدة
        duty_data = DutyData(
            user_id=current_user.id,
            duty_data=json.dumps(data, ensure_ascii=False),
            timestamp=data.get('timestamp', datetime.now().isoformat())
        )

        db.session.add(duty_data)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حفظ البيانات بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@duties_bp.route('/api/load-data', methods=['GET'])
@login_required
def load_duties_data():
    """تحميل بيانات كشف الواجبات"""
    try:
        # البحث عن آخر بيانات محفوظة للمستخدم
        duty_data = DutyData.query.filter_by(user_id=current_user.id).order_by(DutyData.created_at.desc()).first()

        if duty_data:
            data = json.loads(duty_data.duty_data)
            return jsonify({
                'success': True,
                'data': data,
                'timestamp': duty_data.timestamp
            })
        else:
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات محفوظة'
            })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@duties_bp.route('/api/get-location-personnel/<int:location_id>', methods=['GET'])
@login_required
def get_location_personnel(location_id):
    """الحصول على قائمة الأفراد المعينين لموقع محدد"""
    try:
        # التحقق من وجود الموقع
        location = Location.query.get_or_404(location_id)

        # الحصول على الأفراد المعينين للموقع
        assignments = LocationPersonnel.query.filter_by(
            location_id=location_id,
            is_active=True
        ).join(Personnel).all()

        personnel_data = []
        for assignment in assignments:
            personnel = assignment.personnel
            personnel_data.append({
                'id': personnel.id,
                'name': personnel.name,
                'rank': personnel.rank,
                'military_number': personnel.personnel_id,
                'assignment_date': assignment.assignment_date.strftime('%Y-%m-%d') if assignment.assignment_date else None,
                'notes': assignment.notes
            })

        return jsonify({
            'success': True,
            'personnel': personnel_data,
            'location_name': location.name,
            'count': len(personnel_data)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/save-assignment-data', methods=['POST'])
@login_required
def save_assignment_data():
    """حفظ بيانات كشف الواجبات الشامل"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات للحفظ'
            })

        # استخراج البيانات
        assignment_data = data.get('assignmentData', {})
        patrol_data = data.get('patrolData', {})
        shifts_data = data.get('shiftsData', {})
        site_personnel_map = data.get('sitePersonnelMap', {})

        # تحديد الموقع
        location_id = assignment_data.get('selectedSiteId')
        if not location_id:
            # استخدام أول موقع متاح
            first_location = Location.query.filter_by(status='نشط').first()
            if first_location:
                location_id = first_location.id
            else:
                return jsonify({
                    'success': False,
                    'error': 'لا توجد مواقع متاحة'
                })

        # التحقق من وجود الموقع
        location = Location.query.get(location_id)
        if not location:
            return jsonify({
                'success': False,
                'error': 'الموقع المحدد غير موجود'
            })

        # تحديد تاريخ ووقت الواجب
        duty_date = datetime.now().date()
        duty_time = datetime.now().time()

        # البحث عن سجل موجود للمستخدم الحالي واليوم الحالي
        existing_duty = DutyData.query.filter_by(
            user_id=current_user.id,
            duty_date=duty_date,
            location_id=location_id
        ).first()

        if existing_duty:
            # تحديث السجل الموجود
            existing_duty.duty_data = json.dumps({
                'assignmentData': assignment_data,
                'patrolData': patrol_data,
                'shiftsData': shifts_data,
                'sitePersonnelMap': site_personnel_map,
                'timestamp': data.get('timestamp', datetime.now().isoformat())
            }, ensure_ascii=False)
            existing_duty.updated_at = get_saudi_now()

            print(f"✅ تم تحديث بيانات كشف الواجبات الموجودة (ID: {existing_duty.id})")
        else:
            # إنشاء سجل جديد
            duty_data_record = DutyData(
                location_id=location_id,
                user_id=current_user.id,
                duty_date=duty_date,
                duty_time=duty_time,
                duty_data=json.dumps({
                    'assignmentData': assignment_data,
                    'patrolData': patrol_data,
                    'shiftsData': shifts_data,
                    'sitePersonnelMap': site_personnel_map,
                    'timestamp': data.get('timestamp', datetime.now().isoformat())
                }, ensure_ascii=False),
                notes=f"كشف واجبات - {assignment_data.get('dayName', '')}"
            )

            db.session.add(duty_data_record)
            print(f"✅ تم إنشاء سجل جديد لكشف الواجبات")

        # حفظ التغييرات
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حفظ بيانات كشف الواجبات بنجاح',
            'location_name': location.name
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ بيانات كشف الواجبات: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في حفظ البيانات: {str(e)}'
        })



@duties_bp.route('/api/save-duty', methods=['POST'])
@login_required
def save_duty():
    """حفظ كشف واجب جديد"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات'})
        
        # التحقق من البيانات المطلوبة
        required_fields = ['location_id', 'duty_date', 'duty_time', 'duty_data']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'حقل مطلوب مفقود: {field}'})
        
        # تحويل التاريخ والوقت
        try:
            duty_date = datetime.strptime(data['duty_date'], '%Y-%m-%d').date()
            duty_time = datetime.strptime(data['duty_time'], '%H:%M').time()
        except ValueError as e:
            return jsonify({'success': False, 'error': f'تنسيق التاريخ أو الوقت غير صحيح: {str(e)}'})
        
        # إنشاء سجل كشف الواجب
        duty_data = DutyData(
            location_id=data['location_id'],
            user_id=current_user.id,
            duty_date=duty_date,
            duty_time=duty_time,
            duty_data=json.dumps(data['duty_data'], ensure_ascii=False),
            notes=data.get('notes', '')
        )
        
        db.session.add(duty_data)
        db.session.flush()  # للحصول على ID
        
        # حفظ أفراد الواجب
        if 'personnel' in data and data['personnel']:
            for person_data in data['personnel']:
                duty_personnel = DutyPersonnel(
                    duty_data_id=duty_data.id,
                    personnel_id=person_data['personnel_id'],
                    duty_position=person_data.get('position', ''),
                    duty_status=person_data.get('status', 'حاضر'),
                    notes=person_data.get('notes', '')
                )
                db.session.add(duty_personnel)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حفظ كشف الواجب بنجاح',
            'duty_id': duty_data.id
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-duties', methods=['GET'])
@login_required
def get_duties():
    """الحصول على قائمة كشوفات الواجبات"""
    try:
        # معاملات البحث
        location_id = request.args.get('location_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # بناء الاستعلام
        query = db.session.query(DutyData, Location).join(
            Location, DutyData.location_id == Location.id
        )
        
        # تطبيق المرشحات
        if location_id:
            query = query.filter(DutyData.location_id == location_id)
        
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(DutyData.duty_date >= start_date_obj)
        
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(DutyData.duty_date <= end_date_obj)
        
        # ترتيب النتائج
        query = query.order_by(DutyData.duty_date.desc(), DutyData.duty_time.desc())
        
        duties = query.all()
        
        duties_data = []
        for duty_data, location in duties:
            # الحصول على أفراد الواجب
            personnel = db.session.query(DutyPersonnel, Personnel).join(
                Personnel, DutyPersonnel.personnel_id == Personnel.id
            ).filter(DutyPersonnel.duty_data_id == duty_data.id).all()
            
            personnel_list = []
            for duty_personnel, person in personnel:
                personnel_list.append({
                    'id': person.id,
                    'name': person.name,
                    'rank': person.rank,
                    'position': duty_personnel.duty_position,
                    'status': duty_personnel.duty_status,
                    'notes': duty_personnel.notes
                })
            
            duties_data.append({
                'id': duty_data.id,
                'location': {
                    'id': location.id,
                    'name': location.name,
                    'serial_number': location.serial_number
                },
                'duty_date': duty_data.duty_date.strftime('%Y-%m-%d'),
                'duty_time': duty_data.duty_time.strftime('%H:%M'),
                'duty_data': json.loads(duty_data.duty_data),
                'personnel': personnel_list,
                'notes': duty_data.notes,
                'created_at': duty_data.created_at.strftime('%Y-%m-%d %H:%M')
            })
        
        return jsonify({
            'success': True,
            'duties': duties_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-duty/<int:duty_id>', methods=['GET'])
@login_required
def get_duty(duty_id):
    """الحصول على تفاصيل كشف واجب محدد"""
    try:
        duty_data = DutyData.query.get_or_404(duty_id)
        location = Location.query.get(duty_data.location_id)
        
        # الحصول على أفراد الواجب
        personnel = db.session.query(DutyPersonnel, Personnel).join(
            Personnel, DutyPersonnel.personnel_id == Personnel.id
        ).filter(DutyPersonnel.duty_data_id == duty_id).all()
        
        personnel_list = []
        for duty_personnel, person in personnel:
            personnel_list.append({
                'id': person.id,
                'personnel_id': person.personnel_id,
                'name': person.name,
                'rank': person.rank,
                'position': duty_personnel.duty_position,
                'status': duty_personnel.duty_status,
                'notes': duty_personnel.notes
            })
        
        duty_info = {
            'id': duty_data.id,
            'location': {
                'id': location.id,
                'name': location.name,
                'serial_number': location.serial_number
            },
            'duty_date': duty_data.duty_date.strftime('%Y-%m-%d'),
            'duty_time': duty_data.duty_time.strftime('%H:%M'),
            'duty_data': json.loads(duty_data.duty_data),
            'personnel': personnel_list,
            'notes': duty_data.notes,
            'created_at': duty_data.created_at.strftime('%Y-%m-%d %H:%M'),
            'updated_at': duty_data.updated_at.strftime('%Y-%m-%d %H:%M')
        }
        
        return jsonify({
            'success': True,
            'duty': duty_info
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/delete-duty/<int:duty_id>', methods=['DELETE'])
@login_required
def delete_duty(duty_id):
    """حذف كشف واجب"""
    try:
        duty_data = DutyData.query.get_or_404(duty_id)
        
        # حذف أفراد الواجب أولاً
        DutyPersonnel.query.filter_by(duty_data_id=duty_id).delete()
        
        # حذف كشف الواجب
        db.session.delete(duty_data)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف كشف الواجب بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

# Routes لقوالب الواجبات

@duties_bp.route('/api/save-template', methods=['POST'])
@login_required
def save_template():
    """حفظ قالب واجب جديد"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات'})

        # التحقق من البيانات المطلوبة
        required_fields = ['name', 'location_id', 'template_data']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'حقل مطلوب مفقود: {field}'})

        # إنشاء قالب جديد
        template = DutyTemplate(
            name=data['name'],
            location_id=data['location_id'],
            template_data=json.dumps(data['template_data'], ensure_ascii=False),
            created_by=current_user.id
        )

        db.session.add(template)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حفظ القالب بنجاح',
            'template_id': template.id
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-templates/<int:location_id>', methods=['GET'])
@login_required
def get_templates(location_id):
    """الحصول على قوالب الواجبات لموقع معين"""
    try:
        templates = DutyTemplate.query.filter_by(
            location_id=location_id,
            is_active=True
        ).order_by(DutyTemplate.name).all()

        templates_data = []
        for template in templates:
            templates_data.append({
                'id': template.id,
                'name': template.name,
                'template_data': json.loads(template.template_data),
                'created_at': template.created_at.strftime('%Y-%m-%d %H:%M')
            })

        return jsonify({
            'success': True,
            'templates': templates_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/delete-template/<int:template_id>', methods=['DELETE'])
@login_required
def delete_template(template_id):
    """حذف قالب واجب"""
    try:
        template = DutyTemplate.query.get_or_404(template_id)

        # تعطيل القالب بدلاً من حذفه
        template.is_active = False
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف القالب بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/save-receipt', methods=['POST'])
@login_required
def save_receipt():
    """حفظ كشف الواجبات الكامل"""
    try:
        print("🔄 بدء عملية حفظ الكشف...")

        data = request.get_json()
        print(f"📋 البيانات المستلمة: {data is not None}")

        if not data:
            print("❌ لا توجد بيانات")
            return jsonify({'success': False, 'message': 'لا توجد بيانات'})

        print(f"👤 المستخدم الحالي: {current_user.id}")

        # الحصول على أول موقع متاح أو إنشاء موقع افتراضي
        first_location = Location.query.filter_by(status='نشط').first()
        if not first_location:
            # إنشاء موقع افتراضي إذا لم يوجد
            first_location = Location(
                name='موقع افتراضي',
                type='عام',
                serial_number='DEFAULT001',
                description='موقع افتراضي للكشوفات',
                status='نشط'
            )
            db.session.add(first_location)
            db.session.flush()  # للحصول على ID

        # إنشاء سجل كشف الواجب
        duty_data = DutyData(
            location_id=first_location.id,
            user_id=current_user.id,
            duty_date=datetime.now().date(),
            duty_time=datetime.now().time(),
            duty_data=json.dumps(data, ensure_ascii=False),
            notes=f"كشف واجبات - {data.get('day_name', '')}"
        )

        print("💾 إضافة البيانات إلى قاعدة البيانات...")
        db.session.add(duty_data)
        db.session.commit()

        print(f"✅ تم حفظ الكشف بنجاح - ID: {duty_data.id}")

        return jsonify({
            'success': True,
            'message': 'تم حفظ الكشف بنجاح',
            'duty_id': duty_data.id
        })

    except Exception as e:
        print(f"❌ خطأ في حفظ الكشف: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        })

@duties_bp.route('/api/save-draft', methods=['POST'])
@login_required
def save_draft():
    """حفظ مسودة كشف الواجب"""
    try:
        data = request.get_json()
        print(f"💾 حفظ مسودة: {len(str(data))} حرف")

        # البحث عن مسودة موجودة للمستخدم الحالي
        existing_draft = DutyTemplate.query.filter_by(
            name=f"مسودة_{current_user.id}",
            created_by=current_user.id
        ).first()

        if existing_draft:
            # تحديث المسودة الموجودة
            existing_draft.template_data = json.dumps(data, ensure_ascii=False)
            existing_draft.updated_at = get_saudi_now()
            print(f"✅ تم تحديث المسودة الموجودة")
        else:
            # إنشاء مسودة جديدة
            # نحتاج إلى موقع افتراضي للمسودات
            default_location = Location.query.filter_by(status='نشط').first()
            if not default_location:
                # إنشاء موقع افتراضي للمسودات
                default_location = Location(
                    name='مسودات',
                    type='نظام',
                    serial_number='DRAFT001',
                    description='موقع افتراضي للمسودات',
                    status='نشط'
                )
                db.session.add(default_location)
                db.session.flush()

            draft = DutyTemplate(
                name=f"مسودة_{current_user.id}",
                template_data=json.dumps(data, ensure_ascii=False),
                created_by=current_user.id,
                location_id=default_location.id
            )
            db.session.add(draft)
            print(f"✅ تم إنشاء مسودة جديدة")

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حفظ المسودة بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ المسودة: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/load-draft', methods=['GET'])
@login_required
def load_draft():
    """تحميل مسودة كشف الواجب"""
    try:
        # البحث عن مسودة المستخدم الحالي
        draft = DutyTemplate.query.filter_by(
            name=f"مسودة_{current_user.id}",
            created_by=current_user.id
        ).first()

        if draft:
            draft_data = json.loads(draft.template_data)
            print(f"✅ تم تحميل المسودة: {len(str(draft_data))} حرف")

            return jsonify({
                'success': True,
                'data': draft_data,
                'last_updated': draft.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            print(f"⚠️ لا توجد مسودة محفوظة للمستخدم {current_user.id}")
            return jsonify({
                'success': False,
                'message': 'لا توجد مسودة محفوظة'
            })

    except Exception as e:
        print(f"❌ خطأ في تحميل المسودة: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-personnel/<int:personnel_id>')
@login_required
def get_personnel_by_id(personnel_id):
    """الحصول على بيانات فرد واحد بالرقم"""
    try:
        personnel = Personnel.query.get(personnel_id)
        if personnel:
            return jsonify({
                'success': True,
                'personnel': {
                    'id': personnel.id,
                    'name': personnel.name,
                    'rank': personnel.rank,
                    'military_number': personnel.personnel_id,  # تصحيح: استخدام personnel_id بدلاً من military_number
                    'display_name': f"{personnel.name} ({personnel.rank})"
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': 'الفرد غير موجود'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/clear-draft', methods=['POST'])
@login_required
def clear_draft():
    """مسح المسودة من قاعدة البيانات"""
    try:
        print(f"🗑️ مسح مسودة المستخدم {current_user.id}")

        # حذف المسودة الحالية للمستخدم
        draft = DutyTemplate.query.filter_by(user_id=current_user.id).first()
        if draft:
            db.session.delete(draft)
            db.session.commit()
            print("✅ تم مسح المسودة من قاعدة البيانات")
            return jsonify({'success': True, 'message': 'تم مسح المسودة'})
        else:
            print("⚠️ لا توجد مسودة لمسحها")
            return jsonify({'success': True, 'message': 'لا توجد مسودة لمسحها'})

    except Exception as e:
        print(f"❌ خطأ في مسح المسودة: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/export-excel', methods=['POST'])
@login_required
def export_excel():
    """تصدير كشف الواجبات إلى Excel أو CSV"""
    try:
        print("📥 تم استلام طلب تصدير كشف الواجبات")
        data = request.get_json()
        print(f"📦 البيانات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'message': 'لا توجد بيانات للتصدير'})

        # استخراج البيانات
        duty_info = {
            'hijri_date': data.get('hijriDate', '') or data.get('info', {}).get('hijri_date', ''),
            'gregorian_date': data.get('gregorianDate', '') or data.get('info', {}).get('gregorian_date', ''),
            'day_name': data.get('dayName', '') or data.get('info', {}).get('day_name', ''),
            'receipt_number': data.get('receiptNumber', '') or data.get('info', {}).get('receipt_number', '')
        }

        # تحويل البيانات من تنسيق المصفوفة إلى تنسيق الجدول
        def convert_array_to_table(array_data):
            if not array_data or len(array_data) < 1:
                return {'headers': [], 'rows': []}

            headers = array_data[0] if len(array_data) > 0 else []
            rows = array_data[1:] if len(array_data) > 1 else []

            return {'headers': headers, 'rows': rows}

        # استخراج البيانات بالتنسيق الصحيح
        duty_table = data.get('dutyTableData', {})
        if not duty_table and data.get('main_table'):
            duty_table = convert_array_to_table(data.get('main_table', []))

        patrol_table = data.get('patrolTableData', {})
        if not patrol_table and data.get('patrol_table'):
            patrol_table = convert_array_to_table(data.get('patrol_table', []))

        shifts_table = data.get('shiftsTableData', {})
        if not shifts_table and data.get('shifts_table'):
            shifts_table = convert_array_to_table(data.get('shifts_table', []))

        print(f"📋 معلومات الكشف: {duty_info}")
        print(f"📊 جدول الواجبات: {duty_table}")
        print(f"📊 جدول الدوريات: {patrol_table}")
        print(f"📊 جدول المناوبات: {shifts_table}")
        print(f"📊 عدد صفوف الواجبات: {len(duty_table.get('rows', []))}")
        print(f"📊 عدد صفوف الدوريات: {len(patrol_table.get('rows', []))}")
        print(f"📊 عدد صفوف المناوبات: {len(shifts_table.get('rows', []))}")

        # إنشاء اسم الملف
        filename = f"كشف_الواجبات_{duty_info.get('gregorian_date', datetime.now().strftime('%Y-%m-%d'))}"

        # محاولة إنشاء ملف Excel أولاً
        if EXCEL_AVAILABLE:
            try:
                excel_file = create_duty_excel(duty_table, patrol_table, shifts_table, duty_info, filename)
                if excel_file:
                    print("✅ تم إنشاء ملف Excel بنجاح")
                    return send_file(
                        excel_file,
                        download_name=f"{filename}.xlsx",
                        as_attachment=True,
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )
            except Exception as e:
                print(f"❌ فشل في إنشاء Excel: {str(e)}")

        # إنشاء ملف CSV كبديل
        print("📄 إنشاء ملف CSV كبديل...")
        csv_file = create_duty_csv(duty_table, patrol_table, shifts_table, duty_info, filename)

        if csv_file:
            print("✅ تم إنشاء ملف CSV بنجاح")
            return send_file(
                csv_file,
                download_name=f"{filename}.csv",
                as_attachment=True,
                mimetype='text/csv; charset=utf-8'
            )
        else:
            print("❌ فشل في إنشاء ملف CSV")
            return jsonify({'success': False, 'message': 'فشل في إنشاء الملف'})

    except Exception as e:
        print(f"❌ خطأ في التصدير: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'خطأ في التصدير: {str(e)}'})

def create_duty_excel(duty_table, patrol_table, shifts_table, duty_info, filename):
    """إنشاء ملف Excel لكشف الواجبات"""
    try:
        if not EXCEL_AVAILABLE:
            return None

        print(f"📊 بدء إنشاء ملف Excel: {filename}")

        # إنشاء buffer في الذاكرة
        output = BytesIO()

        # إنشاء workbook
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})

        # تنسيقات الخلايا (مطابقة لكشف الاستلامات)
        # تنسيق معلومات التاريخ في الأعلى
        date_header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#FFFFFF',
            'border': 1,
            'font_size': 12,
            'font_name': 'Arial'
        })

        # تنسيق عناوين الجداول
        table_title_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D7E4BC',
            'border': 1,
            'font_size': 12,
            'font_name': 'Arial'
        })

        # تنسيق رؤوس الأعمدة
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D7E4BC',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # تنسيق الخلايا العادية
        cell_format = workbook.add_format({
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # تنسيق خلايا الأرقام
        number_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#F0F0F0',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # إنشاء ورقة العمل
        worksheet = workbook.add_worksheet('كشف الواجبات')
        worksheet.right_to_left()

        row = 0

        # ===== معلومات التاريخ في الأعلى (نفس تنسيق كشف الاستلامات) =====
        gregorian_date = duty_info.get('gregorian_date', '')
        hijri_date = duty_info.get('hijri_date', '')
        receipt_number = duty_info.get('receipt_number', '')
        day_name = duty_info.get('day_name', '')

        # الصف الأول: التاريخ الميلادي والتاريخ الهجري
        worksheet.write(row, 0, gregorian_date, date_header_format)
        worksheet.write(row, 1, 'التاريخ الميلادي', date_header_format)
        worksheet.write(row, 2, hijri_date, date_header_format)
        worksheet.write(row, 3, 'التاريخ الهجري', date_header_format)

        # الصف الثاني: رقم الكشف واليوم
        row += 1
        worksheet.write(row, 0, receipt_number, date_header_format)
        worksheet.write(row, 1, 'رقم الكشف', date_header_format)
        worksheet.write(row, 2, day_name, date_header_format)
        worksheet.write(row, 3, 'اليوم', date_header_format)

        row += 2  # مسافة بين المعلومات والجداول

        # ===== قسم كشف الواجبات الرئيسي =====
        print(f"🔍 فحص جدول الواجبات: headers={duty_table.get('headers')}, rows={len(duty_table.get('rows', []))}")
        if duty_table.get('headers') and duty_table.get('rows'):
            # عنوان كشف الواجبات
            max_cols = max(len(duty_table['headers']), 8)
            worksheet.merge_range(row, 0, row, max_cols - 1,
                                'كشف الواجبات الرئيسي', table_title_format)
            row += 1

            # كتابة بيانات كشف الواجبات
            # العناوين مع إضافة عمود الرقم
            worksheet.write(row, 0, 'الرقم', header_format)
            worksheet.set_column(0, 0, 8)
            for col, header in enumerate(duty_table['headers'], 1):
                worksheet.write(row, col, header, header_format)
                worksheet.set_column(col, col, 15)
            row += 1

            # البيانات مع إضافة الترقيم
            for row_idx, duty_row in enumerate(duty_table['rows']):
                # كتابة رقم الصف
                worksheet.write(row, 0, row_idx + 1, number_format)
                # كتابة باقي البيانات
                for col, cell_data in enumerate(duty_row, 1):
                    worksheet.write(row, col, cell_data or '', cell_format)
                row += 1
            row += 2
        else:
            print("⚠️ لا توجد بيانات في جدول الواجبات الرئيسي أو البيانات غير صحيحة")

        # ===== قسم كشف الدوريات =====
        print(f"🔍 فحص جدول الدوريات: headers={patrol_table.get('headers')}, rows={len(patrol_table.get('rows', []))}")
        if patrol_table.get('headers') and patrol_table.get('rows'):
            # عنوان كشف الدوريات
            max_cols = max(len(patrol_table['headers']), 8)
            worksheet.merge_range(row, 0, row, max_cols - 1,
                                'كشف الدوريات', table_title_format)
            row += 1

            # العناوين مع إضافة عمود الرقم
            worksheet.write(row, 0, 'الرقم', header_format)
            worksheet.set_column(0, 0, 8)
            for col, header in enumerate(patrol_table['headers'], 1):
                worksheet.write(row, col, header, header_format)
                worksheet.set_column(col, col, 15)
            row += 1

            # البيانات مع إضافة الترقيم
            for row_idx, patrol_row in enumerate(patrol_table['rows']):
                # كتابة رقم الصف
                worksheet.write(row, 0, row_idx + 1, number_format)
                # كتابة باقي البيانات
                for col, cell_data in enumerate(patrol_row, 1):
                    worksheet.write(row, col, cell_data or '', cell_format)
                row += 1
            row += 2
        else:
            print("⚠️ لا توجد بيانات في جدول الدوريات أو البيانات غير صحيحة")

        # ===== قسم كشف المناوبات =====
        print(f"🔍 فحص جدول المناوبات: headers={shifts_table.get('headers')}, rows={len(shifts_table.get('rows', []))}")
        if shifts_table.get('headers') and shifts_table.get('rows'):
            # عنوان كشف المناوبات
            max_cols = max(len(shifts_table['headers']), 8)
            worksheet.merge_range(row, 0, row, max_cols - 1,
                                'كشف المناوبات', table_title_format)
            row += 1

            # العناوين مع إضافة عمود الرقم
            worksheet.write(row, 0, 'الرقم', header_format)
            worksheet.set_column(0, 0, 8)
            for col, header in enumerate(shifts_table['headers'], 1):
                worksheet.write(row, col, header, header_format)
                worksheet.set_column(col, col, 15)
            row += 1

            # البيانات مع إضافة الترقيم
            for row_idx, shifts_row in enumerate(shifts_table['rows']):
                # كتابة رقم الصف
                worksheet.write(row, 0, row_idx + 1, number_format)
                # كتابة باقي البيانات
                for col, cell_data in enumerate(shifts_row, 1):
                    worksheet.write(row, col, cell_data or '', cell_format)
                row += 1
        else:
            print("⚠️ لا توجد بيانات في جدول المناوبات أو البيانات غير صحيحة")

        # إعدادات الطباعة
        worksheet.set_landscape()
        worksheet.set_paper(9)  # A4
        worksheet.fit_to_pages(1, 0)
        worksheet.set_margins(0.3, 0.3, 0.5, 0.5)

        # إغلاق الـ workbook
        workbook.close()
        output.seek(0)

        print(f"✅ تم إنشاء ملف Excel بنجاح، الحجم: {len(output.getvalue())} بايت")
        return output

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_duty_csv(duty_table, patrol_table, shifts_table, duty_info, filename):
    """إنشاء ملف CSV كبديل"""
    try:
        print(f"📄 بدء إنشاء ملف CSV: {filename}")

        # إنشاء ملف مؤقت
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8-sig')

        writer = csv.writer(temp_file)

        # كتابة معلومات الكشف
        writer.writerow(['كشف الواجبات - الفريق الأمني'])
        writer.writerow(['=' * 60])
        writer.writerow([])

        if duty_info.get('day_name'):
            writer.writerow(['اليوم:', duty_info['day_name']])

        if duty_info.get('hijri_date'):
            writer.writerow(['التاريخ الهجري:', duty_info['hijri_date']])

        if duty_info.get('gregorian_date'):
            writer.writerow(['التاريخ الميلادي:', duty_info['gregorian_date']])

        if duty_info.get('receipt_number'):
            writer.writerow(['رقم الكشف:', duty_info['receipt_number']])

        writer.writerow([])

        # كتابة جدول الواجبات الرئيسي
        if duty_table.get('headers') and duty_table.get('rows'):
            writer.writerow(['كشف الواجبات الرئيسي'])
            writer.writerow(['-' * 40])
            writer.writerow(duty_table['headers'])

            for row in duty_table['rows']:
                writer.writerow([cell or '' for cell in row])

            writer.writerow([])

        # كتابة جدول الدوريات
        if patrol_table.get('headers') and patrol_table.get('rows'):
            writer.writerow(['كشف الدوريات'])
            writer.writerow(['-' * 40])
            writer.writerow(patrol_table['headers'])

            for row in patrol_table['rows']:
                writer.writerow([cell or '' for cell in row])

            writer.writerow([])

        # كتابة جدول المناوبات
        if shifts_table.get('headers') and shifts_table.get('rows'):
            writer.writerow(['كشف المناوبات'])
            writer.writerow(['-' * 40])
            writer.writerow(shifts_table['headers'])

            for row in shifts_table['rows']:
                writer.writerow([cell or '' for cell in row])

        temp_file.close()

        print(f"✅ تم إنشاء ملف CSV بنجاح: {temp_file.name}")
        return temp_file.name

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف CSV: {str(e)}")
        return None


@duties_bp.route('/api/load-assignment-data', methods=['GET'])
@login_required
def load_assignment_data():
    """تحميل بيانات كشف الواجبات المحفوظة"""
    try:
        # البحث عن آخر كشف واجبات للمستخدم الحالي
        latest_duty = DutyData.query.filter_by(
            user_id=current_user.id
        ).order_by(DutyData.created_at.desc()).first()

        if latest_duty:
            try:
                # تحليل البيانات المحفوظة
                saved_data = json.loads(latest_duty.duty_data)

                return jsonify({
                    'success': True,
                    'data': saved_data,
                    'location_id': latest_duty.location_id,
                    'duty_date': latest_duty.duty_date.isoformat(),
                    'last_updated': latest_duty.updated_at.isoformat() if latest_duty.updated_at else latest_duty.created_at.isoformat()
                })

            except json.JSONDecodeError:
                print(f"⚠️ خطأ في تحليل البيانات المحفوظة للواجب {latest_duty.id}")
                return jsonify({
                    'success': False,
                    'error': 'خطأ في تحليل البيانات المحفوظة'
                })
        else:
            return jsonify({
                'success': True,
                'data': None,
                'message': 'لا توجد بيانات محفوظة'
            })

    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات كشف الواجبات: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في تحميل البيانات: {str(e)}'
        })

@duties_bp.route('/api/save-duty-data', methods=['POST'])
@login_required
def save_duty_data():
    """حفظ بيانات كشف الواجبات في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ بيانات كشف الواجبات")
        data = request.get_json()
        print(f"📦 البيانات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        # الحصول على أول موقع متاح أو إنشاء موقع افتراضي
        first_location = Location.query.filter_by(status='نشط').first()
        if not first_location:
            # إنشاء موقع افتراضي إذا لم يوجد
            first_location = Location(
                name='موقع افتراضي',
                type='عام',
                serial_number='DEFAULT001',
                description='موقع افتراضي للكشوفات',
                status='نشط'
            )
            db.session.add(first_location)
            db.session.flush()

        # البحث عن سجل موجود للمستخدم الحالي واليوم الحالي
        today = datetime.now().date()
        existing_duty = DutyData.query.filter_by(
            user_id=current_user.id,
            duty_date=today
        ).first()

        if existing_duty:
            # تحديث السجل الموجود
            existing_duty.duty_data = json.dumps(data, ensure_ascii=False)
            existing_duty.updated_at = get_saudi_now()
            print(f"✅ تم تحديث بيانات كشف الواجبات الموجودة")
        else:
            # إنشاء سجل جديد
            duty_data = DutyData(
                location_id=first_location.id,
                user_id=current_user.id,
                duty_date=today,
                duty_time=datetime.now().time(),
                duty_data=json.dumps(data, ensure_ascii=False),
                notes=f"كشف واجبات - {data.get('timestamp', '')}"
            )
            db.session.add(duty_data)
            print(f"✅ تم إنشاء سجل جديد لكشف الواجبات")

        db.session.commit()
        print(f"✅ تم حفظ بيانات كشف الواجبات بنجاح")

        return jsonify({
            'success': True,
            'message': 'تم حفظ البيانات بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ بيانات كشف الواجبات: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في حفظ البيانات: {str(e)}'
        }), 500


@duties_bp.route('/api/save-patrol-data', methods=['POST'])
@login_required
def save_patrol_data():
    """حفظ بيانات جدول الدوريات في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ بيانات جدول الدوريات")
        data = request.get_json()
        print(f"📦 بيانات الدوريات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        # حذف البيانات السابقة للمستخدم الحالي
        PatrolData.query.filter_by(user_id=current_user.id).delete()

        # حفظ البيانات الجديدة
        patrol_data = PatrolData(
            user_id=current_user.id,
            patrol_data=json.dumps(data, ensure_ascii=False)
        )
        db.session.add(patrol_data)
        db.session.commit()

        print(f"✅ تم حفظ بيانات جدول الدوريات بنجاح")

        return jsonify({
            'success': True,
            'message': 'تم حفظ بيانات الدوريات بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ بيانات الدوريات: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في حفظ البيانات: {str(e)}'
        }), 500


@duties_bp.route('/api/save-shifts-data', methods=['POST'])
@login_required
def save_shifts_data():
    """حفظ بيانات جدول المناوبين في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ بيانات جدول المناوبين")
        data = request.get_json()
        print(f"📦 بيانات المناوبين المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        # حذف البيانات السابقة للمستخدم الحالي
        ShiftsData.query.filter_by(user_id=current_user.id).delete()

        # حفظ البيانات الجديدة
        shifts_data = ShiftsData(
            user_id=current_user.id,
            shifts_data=json.dumps(data, ensure_ascii=False)
        )
        db.session.add(shifts_data)
        db.session.commit()

        print(f"✅ تم حفظ بيانات جدول المناوبين بنجاح")

        return jsonify({
            'success': True,
            'message': 'تم حفظ بيانات المناوبين بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ بيانات المناوبين: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في حفظ البيانات: {str(e)}'
        }), 500


@duties_bp.route('/api/load-duty-data', methods=['GET'])
@login_required
def load_duty_data():
    """تحميل بيانات كشف الواجبات من قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب تحميل بيانات كشف الواجبات")

        # البحث عن أحدث سجل للمستخدم الحالي
        today = datetime.now().date()
        duty_data = DutyData.query.filter_by(
            user_id=current_user.id,
            duty_date=today
        ).first()

        if duty_data:
            # تحويل البيانات من JSON
            data = json.loads(duty_data.duty_data)
            print(f"✅ تم تحميل بيانات كشف الواجبات بنجاح")

            return jsonify({
                'success': True,
                'data': data
            })
        else:
            print("ℹ️ لا توجد بيانات محفوظة لكشف الواجبات")
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات محفوظة'
            })

    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات كشف الواجبات: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في تحميل البيانات: {str(e)}'
        }), 500


@duties_bp.route('/api/load-patrol-data', methods=['GET'])
@login_required
def load_patrol_data():
    """تحميل بيانات جدول الدوريات من قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب تحميل بيانات جدول الدوريات")

        # البحث عن أحدث سجل للمستخدم الحالي
        patrol_data = PatrolData.query.filter_by(user_id=current_user.id).first()

        if patrol_data:
            # تحويل البيانات من JSON
            data = json.loads(patrol_data.patrol_data)
            print(f"✅ تم تحميل بيانات جدول الدوريات بنجاح")

            return jsonify({
                'success': True,
                'data': data
            })
        else:
            print("ℹ️ لا توجد بيانات محفوظة لجدول الدوريات")
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات محفوظة'
            })

    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات جدول الدوريات: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في تحميل البيانات: {str(e)}'
        }), 500


@duties_bp.route('/api/load-shifts-data', methods=['GET'])
@login_required
def load_shifts_data():
    """تحميل بيانات جدول المناوبين من قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب تحميل بيانات جدول المناوبين")

        # البحث عن أحدث سجل للمستخدم الحالي
        shifts_data = ShiftsData.query.filter_by(user_id=current_user.id).first()

        if shifts_data:
            # تحويل البيانات من JSON
            data = json.loads(shifts_data.shifts_data)
            print(f"✅ تم تحميل بيانات جدول المناوبين بنجاح")

            return jsonify({
                'success': True,
                'data': data
            })
        else:
            print("ℹ️ لا توجد بيانات محفوظة لجدول المناوبين")
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات محفوظة'
            })

    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات جدول المناوبين: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'خطأ في تحميل البيانات: {str(e)}'
        }), 500
