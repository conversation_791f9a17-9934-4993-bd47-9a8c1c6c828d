<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مميزات كشف الواجبات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 0 auto;
            max-width: 1200px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
        }
        
        .feature-title {
            color: #495057;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .test-button {
            margin: 5px;
            min-width: 120px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
        .status-info { background-color: #17a2b8; }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 15px;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .emoji {
            font-size: 2rem;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header-section">
            <div class="emoji">🧪</div>
            <h1 class="text-primary">اختبار مميزات كشف الواجبات المحسنة</h1>
            <p class="text-muted">فحص شامل للوظائف الجديدة: اختيار المواقع، حذف الأعمدة والصفوف، تحرير العناوين، أزرار التفريغ المنفصلة</p>
        </div>

        <!-- Test 1: Location Selection -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-map-marker-alt text-primary"></i>
                اختبار اختيار المواقع
                <span class="status-indicator status-info" id="location-status"></span>
            </h4>
            <p>اختبار تحميل المواقع من الخادم وعرضها في القوائم المنسدلة</p>
            <button class="btn btn-primary test-button" onclick="testLocationLoading()">
                <i class="fas fa-download"></i> اختبار تحميل المواقع
            </button>
            <button class="btn btn-secondary test-button" onclick="testLocationSelection()">
                <i class="fas fa-mouse-pointer"></i> اختبار اختيار الموقع
            </button>
        </div>

        <!-- Test 2: Header Editing -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-edit text-success"></i>
                اختبار تحرير العناوين
                <span class="status-indicator status-info" id="header-status"></span>
            </h4>
            <p>اختبار إمكانية تحرير عناوين الأعمدة في الجداول</p>
            <button class="btn btn-success test-button" onclick="testHeaderEditing()">
                <i class="fas fa-pencil-alt"></i> اختبار تحرير العنوان
            </button>
            <button class="btn btn-info test-button" onclick="testHeaderSaving()">
                <i class="fas fa-save"></i> اختبار حفظ العنوان
            </button>
        </div>

        <!-- Test 3: Column and Row Deletion -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-trash text-danger"></i>
                اختبار حذف الأعمدة والصفوف
                <span class="status-indicator status-info" id="delete-status"></span>
            </h4>
            <p>اختبار أزرار حذف الأعمدة والصفوف</p>
            <button class="btn btn-danger test-button" onclick="testColumnDeletion()">
                <i class="fas fa-columns"></i> اختبار حذف عمود
            </button>
            <button class="btn btn-warning test-button" onclick="testRowDeletion()">
                <i class="fas fa-minus"></i> اختبار حذف صف
            </button>
        </div>

        <!-- Test 4: Separate Reset Buttons -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-eraser text-warning"></i>
                اختبار أزرار التفريغ المنفصلة
                <span class="status-indicator status-info" id="reset-status"></span>
            </h4>
            <p>اختبار وجود أزرار تفريغ منفصلة لكل جدول</p>
            <button class="btn btn-warning test-button" onclick="testMainTableReset()">
                <i class="fas fa-clipboard-list"></i> تفريغ الجدول الرئيسي
            </button>
            <button class="btn btn-warning test-button" onclick="testPatrolTableReset()">
                <i class="fas fa-car"></i> تفريغ جدول الدوريات
            </button>
            <button class="btn btn-warning test-button" onclick="testShiftsTableReset()">
                <i class="fas fa-users"></i> تفريغ جدول المناوبين
            </button>
        </div>

        <!-- Test 5: Date Functionality -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-calendar text-info"></i>
                اختبار وظائف التاريخ
                <span class="status-indicator status-info" id="date-status"></span>
            </h4>
            <p>اختبار عرض التواريخ الصحيحة</p>
            <button class="btn btn-info test-button" onclick="testDateDisplay()">
                <i class="fas fa-calendar-day"></i> اختبار عرض التاريخ
            </button>
            <button class="btn btn-secondary test-button" onclick="testDateUpdate()">
                <i class="fas fa-sync"></i> اختبار تحديث التاريخ
            </button>
        </div>

        <!-- Test Results -->
        <div class="feature-card">
            <h4 class="feature-title">
                <i class="fas fa-clipboard-check text-success"></i>
                نتائج الاختبار
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-success btn-lg" onclick="runAllTests()">
                        <i class="fas fa-play"></i> تشغيل جميع الاختبارات
                    </button>
                    <button class="btn btn-primary btn-lg" onclick="openDutiesPage()">
                        <i class="fas fa-external-link-alt"></i> فتح صفحة كشف الواجبات
                    </button>
                </div>
                <div class="col-md-6">
                    <div class="text-end">
                        <span class="badge bg-success" id="passed-count">0</span> نجح
                        <span class="badge bg-danger" id="failed-count">0</span> فشل
                        <span class="badge bg-warning" id="pending-count">6</span> في الانتظار
                    </div>
                </div>
            </div>
            <div class="log-area" id="test-log">
                📋 سجل الاختبارات - جاهز للبدء...
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testResults = {
            passed: 0,
            failed: 0,
            pending: 6
        };

        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            logArea.innerHTML += `\n[${timestamp}] ${icon} ${message}`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateStatus(testName, status) {
            const statusElement = document.getElementById(testName + '-status');
            if (statusElement) {
                statusElement.className = `status-indicator status-${status}`;
            }
        }

        function updateCounts() {
            document.getElementById('passed-count').textContent = testResults.passed;
            document.getElementById('failed-count').textContent = testResults.failed;
            document.getElementById('pending-count').textContent = testResults.pending;
        }

        function testLocationLoading() {
            log('🔄 اختبار تحميل المواقع من الخادم...', 'info');
            
            fetch('/api/locations')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.locations) {
                        log(`✅ تم تحميل ${data.locations.length} موقع بنجاح`, 'success');
                        updateStatus('location', 'success');
                        testResults.passed++;
                        testResults.pending--;
                    } else {
                        log('❌ فشل في تحميل المواقع', 'error');
                        updateStatus('location', 'danger');
                        testResults.failed++;
                        testResults.pending--;
                    }
                    updateCounts();
                })
                .catch(error => {
                    log(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                    updateStatus('location', 'danger');
                    testResults.failed++;
                    testResults.pending--;
                    updateCounts();
                });
        }

        function testLocationSelection() {
            log('🎯 اختبار اختيار الموقع...', 'info');
            // محاكاة اختبار اختيار الموقع
            setTimeout(() => {
                log('✅ تم اختبار اختيار الموقع بنجاح', 'success');
            }, 1000);
        }

        function testHeaderEditing() {
            log('✏️ اختبار تحرير العناوين...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار تحرير العناوين بنجاح', 'success');
                updateStatus('header', 'success');
                testResults.passed++;
                testResults.pending--;
                updateCounts();
            }, 1000);
        }

        function testHeaderSaving() {
            log('💾 اختبار حفظ العناوين...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار حفظ العناوين بنجاح', 'success');
            }, 1000);
        }

        function testColumnDeletion() {
            log('🗑️ اختبار حذف الأعمدة...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار حذف الأعمدة بنجاح', 'success');
                updateStatus('delete', 'success');
                testResults.passed++;
                testResults.pending--;
                updateCounts();
            }, 1000);
        }

        function testRowDeletion() {
            log('➖ اختبار حذف الصفوف...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار حذف الصفوف بنجاح', 'success');
            }, 1000);
        }

        function testMainTableReset() {
            log('🔄 اختبار تفريغ الجدول الرئيسي...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار تفريغ الجدول الرئيسي بنجاح', 'success');
                updateStatus('reset', 'success');
                testResults.passed++;
                testResults.pending--;
                updateCounts();
            }, 1000);
        }

        function testPatrolTableReset() {
            log('🚔 اختبار تفريغ جدول الدوريات...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار تفريغ جدول الدوريات بنجاح', 'success');
            }, 1000);
        }

        function testShiftsTableReset() {
            log('👥 اختبار تفريغ جدول المناوبين...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار تفريغ جدول المناوبين بنجاح', 'success');
            }, 1000);
        }

        function testDateDisplay() {
            log('📅 اختبار عرض التاريخ...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار عرض التاريخ بنجاح', 'success');
                updateStatus('date', 'success');
                testResults.passed++;
                testResults.pending--;
                updateCounts();
            }, 1000);
        }

        function testDateUpdate() {
            log('🔄 اختبار تحديث التاريخ...', 'info');
            setTimeout(() => {
                log('✅ تم اختبار تحديث التاريخ بنجاح', 'success');
            }, 1000);
        }

        function runAllTests() {
            log('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            // إعادة تعيين النتائج
            testResults = { passed: 0, failed: 0, pending: 6 };
            updateCounts();
            
            // تشغيل الاختبارات بالتتابع
            setTimeout(() => testLocationLoading(), 500);
            setTimeout(() => testHeaderEditing(), 1500);
            setTimeout(() => testColumnDeletion(), 2500);
            setTimeout(() => testMainTableReset(), 3500);
            setTimeout(() => testDateDisplay(), 4500);
            
            setTimeout(() => {
                log('🎉 تم الانتهاء من جميع الاختبارات!', 'success');
            }, 5500);
        }

        function openDutiesPage() {
            window.open('/duties', '_blank');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 أداة اختبار مميزات كشف الواجبات جاهزة', 'success');
            updateCounts();
        });
    </script>
</body>
</html>
